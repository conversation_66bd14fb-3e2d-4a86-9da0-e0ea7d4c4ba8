import axios from "axios"
import { app, BrowserWindow } from "electron"
import fs from "node:fs"
import { <PERSON>shotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"

const isDev = !app.isPackaged
const API_BASE_URL = 'http://localhost:3000'//"http://localhost:3000"   //"https://interview-coder.onrender.com"

export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()
  }

  private async waitForInitialization(mainWindow: BrowserWindow): Promise<void> {
    let attempts = 0;
    const maxAttempts = 100; // Increase from 50 to 100 (10 seconds total)

    try {
      while (attempts < maxAttempts) {
        // Check if the window is valid before executing JavaScript
        if (mainWindow.isDestroyed()) {
          throw new Error("Window was destroyed during initialization check");
        }

        const isInitialized = await mainWindow.webContents.executeJavaScript(
          "window.__IS_INITIALIZED__ || false" // Add fallback to prevent undefined errors
        );

        if (isInitialized) return;
        await new Promise((resolve) => setTimeout(resolve, 100));
        attempts++;
      }
      console.warn("App initialization timed out, proceeding with defaults");
      // Don't throw an error, just continue with defaults
    } catch (error) {
      console.error("Error checking initialization status:", error);
      // Don't rethrow, just continue with defaults
    }
  }

  private async getLanguage(): Promise<string> {
    const mainWindow = this.deps.getMainWindow();
    if (!mainWindow || mainWindow.isDestroyed()) return "java";

    try {
      await this.waitForInitialization(mainWindow);

      // Use a fallback if executeJavaScript fails
      try {
        const language = await mainWindow.webContents.executeJavaScript(
          "window.__LANGUAGE__ || 'java'" // Add fallback
        );

        if (typeof language === "string" && language) {
          return language;
        }
      } catch (error) {
        console.warn("Error accessing language variable:", error);
      }

      // Default fallback
      return "java";
    } catch (error) {
      console.error("Error getting language:", error);
      return "java";
    }
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)
      if (screenshotQueue.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
        return
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          screenshotQueue.map(async (path) => ({
            path,
            preview: await this.screenshotHelper.getImagePreview(path),
            data: fs.readFileSync(path).toString("base64")
          }))
        )

        const result = await this.processScreenshotsHelper(screenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("OpenAI API key not found")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              "OpenAI API key not found in environment variables. Please set the OPEN_AI_API_KEY environment variable."
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        console.log("=============Sending SOLUTION_SUCCESS with data:", result.data)
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)
      if (extraScreenshotQueue.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
        return
      }
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        const screenshots = await Promise.all(
          [
            ...this.screenshotHelper.getScreenshotQueue(),
            ...extraScreenshotQueue
          ].map(async (path) => ({
            path,
            preview: await this.screenshotHelper.getImagePreview(path),
            data: fs.readFileSync(path).toString("base64")
          }))
        )
        console.log(
          "Combined screenshots for processing:",
          screenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          screenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    const MAX_RETRIES = 0
    let retryCount = 0

    while (retryCount <= MAX_RETRIES) {
      try {
        const imageDataList = screenshots.map((screenshot) => screenshot.data)
        const mainWindow = this.deps.getMainWindow()
        const language = await this.getLanguage()
        let problemInfo

        // First API call - extract problem info
        try {
          console.log("============Testingg(extraction call)========")
          const extractResponse = await axios.post(
            `${ API_BASE_URL}/api/extract`,
            // 'http://localhost:3000/api/extract',
            { imageDataList, language },
            {
              signal,
              timeout: 300000,
              validateStatus: function (status) {
                return status < 500
              },
              maxRedirects: 5,
              headers: {
                "Content-Type": "application/json"
              }
            }
          )
          console.log("============Testingg(extraction response)========",extractResponse)
          problemInfo = extractResponse.data

          // Store problem info in AppState
          this.deps.setProblemInfo(problemInfo)

          // Send first success event
          if (mainWindow) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
              problemInfo
            )

            // Generate solutions after successful extraction
            const solutionsResult = await this.generateSolutionsHelper(signal)
            if (solutionsResult.success) {
              // Clear any existing extra screenshots before transitioning to solutions view
              this.screenshotHelper.clearExtraScreenshotQueue()
              mainWindow.webContents.send(
                this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
                solutionsResult.data
              )
              return { success: true, data: solutionsResult.data }
            } else {
              throw new Error(
                solutionsResult.error || "Failed to generate solutions"
              )
            }
          }
        } catch (error: any) {
          // If the request was cancelled, don't retry
          if (axios.isCancel(error)) {
            return {
              success: false,
              error: "Processing was canceled by the user."
            }
          }

          console.error("API Error Details:", {
            status: error.response?.status,
            data: error.response?.data,
            message: error.message,
            code: error.code
          })

          // Handle API-specific errors
          if (
            error.response?.data?.error &&
            typeof error.response.data.error === "string"
          ) {
            if (error.response.data.error.includes("Operation timed out")) {
              throw new Error(
                "Operation timed out after 1 minute. Please try again."
              )
            }
            throw new Error(error.response.data.error)
          }

          // If we get here, it's an unknown error
          throw new Error(error.message || "Server error. Please try again.")
        }
      } catch (error: any) {
        // Log the full error for debugging
        console.error("Processing error details:", {
          message: error.message,
          code: error.code,
          response: error.response?.data,
          retryCount
        })

        // If it's a cancellation or we've exhausted retries, return the error
        if (axios.isCancel(error) || retryCount >= MAX_RETRIES) {
          return { success: false, error: error.message }
        }

        // Increment retry count and continue
        retryCount++
      }
    }

    // If we get here, all retries failed
    return {
      success: false,
      error: "Failed to process after multiple attempts. Please try again."
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo()
      const language = await this.getLanguage()
      console.log("============Testing---------------Language========",language )
      if (!problemInfo) {
        throw new Error("No problem info available")
      }

      const response = await axios.post(
        `${ API_BASE_URL}/api/generate`,
        // 'http://localhost:3000/api/generate',
        { ...problemInfo, language },
        {
          signal,
          timeout: 300000,
          validateStatus: function (status) {
            return status < 500
          },
          maxRedirects: 5,
          headers: {
            "Content-Type": "application/json"
          }
        }
      )
      // Ensure we have all required properties in the response
      const responseData = response.data;

      // If response is a string rather than parsed JSON, try to parse it
      let formattedData = responseData;

      // Check if responseData is a string and needs parsing
      if (typeof responseData === 'string') {
        console.log(".......................Testing---------------string data========",responseData )
        try {
          // Try to find JSON in the string
          const jsonMatch = responseData.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            console.log("============Testing---------------json match========",jsonMatch )
            formattedData = JSON.parse(jsonMatch[0]);
          } else {
            console.log("============Testing---------------no json match========",jsonMatch )
            // If no JSON found, create a basic structure with the content as code
            formattedData = {
              code: responseData,
              thoughts: ["Automatically extracted from text response"],
              time_complexity: "Not specified in response",
              space_complexity: "Not specified in response"
            };
          }
        } catch (error) {
          console.error("Error parsing response string:", error);
          formattedData = {
            code: responseData,
            thoughts: ["Failed to parse structured data from response"],
            time_complexity: "Not specified",
            space_complexity: "Not specified"
          };
        }
      } else if (typeof responseData === 'object' && responseData !== null) {
        console.log(".......................Testing---------------object data========")
        // Response is already an object, use it directly
        formattedData = responseData;
      } else {
        // Fallback for unexpected data types
        console.log(".......................Testing---------------unexpected data========" )
        formattedData = {
          code: String(responseData || ""),
          thoughts: ["Automatically extracted from unstructured response"],
          time_complexity: "Could not determine from response",
          space_complexity: "Could not determine from response"
        };
      }

      // Handle MCQ responses with specific format
      if (language === "mcq") {
        console.log("============Testing--------getting-------MCQ========" )
        console.log("============Testing---------------formattedData========", formattedData )

        // First, try to extract JSON from the response if it's embedded in text
        let mcqData = formattedData;

        // Check if the response contains JSON within the text
        if (typeof formattedData.question_text === 'string' && formattedData.question_text.includes('{')) {
          try {
            // Extract JSON from the text
            const jsonMatch = formattedData.question_text.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              const parsedJson = JSON.parse(jsonStr);

              // If we successfully parsed JSON with MCQ fields, use it
              if (parsedJson.question_text && parsedJson.correct_answer && parsedJson.explanation) {
                mcqData = parsedJson;
                console.log("============Extracted JSON MCQ Data========", mcqData);
              }
            }
          } catch (error) {
            console.log("Failed to parse embedded JSON, using original data");
          }
        }

        // Check if we have the correct MCQ format
        if (mcqData.question_text && mcqData.correct_answer && mcqData.explanation) {
          let cleanQuestionText = mcqData.question_text;
          let correctAnswer = mcqData.correct_answer;
          let explanation = mcqData.explanation;

          // Clean up the question text if it contains analysis prefix
          if (typeof cleanQuestionText === 'string') {
            cleanQuestionText = cleanQuestionText.replace(/^Here is the analysis of the multiple choice question in JSON format:\s*\n*/i, '');
            cleanQuestionText = cleanQuestionText.replace(/^Analyze the following multiple choice question:\s*\n*/i, '');
          }

          // Clean up correct answer if it contains extra text
          if (typeof correctAnswer === 'string') {
            // Extract just the letter/number if there's extra text
            const answerMatch = correctAnswer.match(/^([A-D]|\d+)/i);
            if (answerMatch) {
              correctAnswer = answerMatch[1].toUpperCase();
            }
          }

          const result = {
            question_text: cleanQuestionText,
            correct_answer: correctAnswer,
            explanation: explanation
          };

          console.log("============MCQ Result Format========", result);
          return { success: true, data: result };
        }

        // Fallback handling for incorrect format
        let questionText = formattedData.question_text || formattedData.code || "";
        let correctAnswer = formattedData.correct_answer || "Could not determine the correct answer from the response";
        let explanation = formattedData.explanation || "Could not extract a detailed explanation from the response";

        // Try to extract MCQ data from the raw text if it contains JSON
        if (typeof questionText === 'string' && questionText.includes('{')) {
          try {
            // Look for JSON patterns in the text
            const jsonMatch = questionText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              const jsonStr = jsonMatch[0];
              const parsedJson = JSON.parse(jsonStr);

              if (parsedJson.question_text) {
                questionText = parsedJson.question_text;
              }
              if (parsedJson.correct_answer) {
                correctAnswer = parsedJson.correct_answer;
              }
              if (parsedJson.explanation) {
                explanation = parsedJson.explanation;
              }
            }
          } catch (error) {
            console.log("Failed to parse JSON from fallback text");
          }
        }

        // The server is returning coding solutions instead of MCQ format
        // Let's extract meaningful information and format it properly
        if (questionText.includes('"code":') || questionText.includes('"thoughts":')) {
          // This is a coding solution, not an MCQ - we need to handle this differently
          try {
            // Try to parse the JSON content to extract the actual solution
            const jsonMatch = questionText.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
              let jsonString = jsonMatch[0];

              // Try to fix common JSON issues
              // Replace unescaped newlines in strings
              jsonString = jsonString.replace(/\\n/g, '\\\\n');
              // Fix unescaped quotes in strings
              jsonString = jsonString.replace(/(?<!\\)"/g, '\\"').replace(/\\\\"/g, '\\"');
              // Fix the outer quotes
              jsonString = jsonString.replace(/^"/, '').replace(/"$/, '');

              // Try a more robust approach - extract fields manually instead of parsing JSON
              const codeMatch = jsonString.match(/"code":\s*"([^"]*(?:\\.[^"]*)*)"/) ||
                               jsonString.match(/"code":\s*"([\s\S]*?)(?=",\s*"thoughts")/);
              const thoughtsMatch = jsonString.match(/"thoughts":\s*\[([\s\S]*?)\]/) ||
                                   jsonString.match(/"thoughts":\s*\[([^\]]*)\]/);
              const timeComplexityMatch = jsonString.match(/"time_complexity":\s*"([^"]*)"/) ||
                                         jsonString.match(/"time_complexity":\s*"([\s\S]*?)(?=",|\})/);
              const spaceComplexityMatch = jsonString.match(/"space_complexity":\s*"([^"]*)"/) ||
                                          jsonString.match(/"space_complexity":\s*"([\s\S]*?)(?="\s*\})/);

              // Extract the code content
              let codeContent = "No code provided";
              if (codeMatch && codeMatch[1]) {
                codeContent = codeMatch[1].replace(/\\n/g, '\n').replace(/\\"/g, '"');
              }

              // If no code found in JSON, try to extract from the main text
              if (codeContent === "No code provided") {
                // Look for code blocks in the main text
                const codeBlockMatch = questionText.match(/```[\w]*\n([\s\S]*?)```/) ||
                                      questionText.match(/# Calculate[\s\S]*?print\([^)]*\)/);
                if (codeBlockMatch) {
                  codeContent = codeBlockMatch[1] || codeBlockMatch[0];
                }
              }

              // Extract thoughts
              let thoughtsContent = "";
              if (thoughtsMatch && thoughtsMatch[1]) {
                thoughtsContent = thoughtsMatch[1].replace(/"/g, '').replace(/,/g, ', ');
              }

              // Extract complexity info
              let timeComplexity = timeComplexityMatch ? timeComplexityMatch[1] : "";
              let spaceComplexity = spaceComplexityMatch ? spaceComplexityMatch[1] : "";

              // Create a meaningful MCQ-style response from the coding solution
              questionText = "Server returned a coding solution instead of MCQ format. Here's the solution provided:";
              explanation = `Code Solution:\n${codeContent}`;

              // Add thoughts if available
              if (thoughtsContent) {
                explanation += `\n\nThoughts: ${thoughtsContent}`;
              }

              // Add complexity if available
              if (timeComplexity) {
                explanation += `\n\nTime Complexity: ${timeComplexity}`;
              }
              if (spaceComplexity) {
                explanation += `\n\nSpace Complexity: ${spaceComplexity}`;
              }

              correctAnswer = "Server provided coding solution instead of MCQ";
            }
          } catch (error) {
            console.error("Error parsing JSON from MCQ response:", error);
            questionText = "Server returned invalid MCQ format";
            explanation = "Could not parse the server response properly";
          }
        }

        // Check if we have a proper MCQ with options
        const hasMcqOptions = questionText.includes('A)') ||
                             questionText.includes('B)') ||
                             questionText.includes('1.') ||
                             questionText.includes('2.') ||
                             questionText.includes('a)') ||
                             questionText.includes('(A)');

        if (!hasMcqOptions && !questionText.includes('Server returned')) {
          // If no options found and it's not our custom message, mark as invalid
          questionText = "Invalid MCQ format received from server - no multiple choice options found";
        }

        const result = {
          question_text: questionText,
          correct_answer: correctAnswer,
          explanation: explanation
        };

        console.log("============MCQ Result Format========", result);
        console.log("============MCQ Result being returned with success=true========");
        return { success: true, data: result };
      }

      // Ensure the object has all required properties for non-MCQ responses
      const result = {
        code: formattedData.code || "",
        thoughts: Array.isArray(formattedData.thoughts) ? formattedData.thoughts : ["No specific thoughts provided"],
        time_complexity: formattedData.time_complexity || "Not specified",
        space_complexity: formattedData.space_complexity || "Not specified"
      };

      return { success: true, data: result };
    } catch (error: any) {
      // Error handling remains the same
      const mainWindow = this.deps.getMainWindow()

      // Handle timeout errors (both 504 and axios timeout)
      if (error.code === "ECONNABORTED" || error.response?.status === 504) {
        // Cancel ongoing API requests
        this.cancelOngoingRequests()
        // Clear both screenshot queues
        this.deps.clearQueues()
        // Update view state to queue
        this.deps.setView("queue")
        // Notify renderer to switch view
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("reset-view")
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Request timed out. The server took too long to respond. Please try again."
          )
        }
        return {
          success: false,
          error: "Request timed out. Please try again."
        }
      }

      if (
        error.response?.data?.error?.includes(
          "Please close this window and re-enter a valid Open AI API key."
        )
      ) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.response.data.error }
      }

      return { success: false, error: error.message }
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const imageDataList = screenshots.map((screenshot) => screenshot.data)
      const problemInfo = this.deps.getProblemInfo()
      const language = await this.getLanguage()

      if (!problemInfo) {
        throw new Error("No problem info available")
      }

      const response = await axios.post(
        `${API_BASE_URL}/api/debug`,
        { imageDataList, problemInfo, language },
        {
          signal,
          timeout: 300000,
          validateStatus: function (status) {
            return status < 500
          },
          maxRedirects: 5,
          headers: {
            "Content-Type": "application/json"
          }
        }
      )
      console.log("============Testingg(This is response from on.render)========",response)
      return { success: true, data: response.data }
    } catch (error: any) {
      const mainWindow = this.deps.getMainWindow()

      // Handle cancellation first
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        }
      }

      if (error.response?.data?.error?.includes("Operation timed out")) {
        // Cancel ongoing API requests
        this.cancelOngoingRequests()
        // Clear both screenshot queues
        this.deps.clearQueues()
        // Update view state to queue
        this.deps.setView("queue")
        // Notify renderer to switch view
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send("reset-view")
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Operation timed out after 1 minute. Please try again."
          )
        }
        return {
          success: false,
          error: "Operation timed out after 1 minute. Please try again."
        }
      }

      if (
        error.response?.data?.error?.includes(
          "Please close this window and re-enter a valid Open AI API key."
        )
      ) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.response.data.error }
      }

      return { success: false, error: error.message }
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    // Reset hasDebugged flag
    this.deps.setHasDebugged(false)

    // Clear any pending state
    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      // Send a clear message that processing was cancelled
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }

  /**
   * Process text input directly without screenshots
   * @param text The text to process
   * @param language The programming language
   */
  public async processTextInput(text: string, language: string): Promise<{ success: boolean; data?: any; error?: string }> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return { success: false, error: "No main window available" }

    // Notify UI that processing has started
    mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)

    try {
      // Initialize AbortController
      this.currentProcessingAbortController = new AbortController()
      const { signal } = this.currentProcessingAbortController

      // Create problem info directly from text
      const problemInfo = { problemText: text }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo)

      // Send problem extracted event
      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
        problemInfo
      )

      // Generate solutions
      const solutionsResult = await this.generateSolutionsHelper(signal)
      if (solutionsResult.success) {
        // Clear any existing extra screenshots before transitioning to solutions view
        this.screenshotHelper.clearExtraScreenshotQueue()
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          solutionsResult.data
        )
        // Set view to solutions
        this.deps.setView("solutions")
        return { success: true, data: solutionsResult.data }
      } else {
        throw new Error(
          solutionsResult.error || "Failed to generate solutions"
        )
      }
    } catch (error: any) {
      // Handle errors
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        }
      }

      console.error("Text processing error:", error)

      mainWindow.webContents.send(
        this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
        error.message || "An error occurred while processing text input"
      )

      return {
        success: false,
        error: error.message || "An error occurred while processing text input"
      }
    } finally {
      this.currentProcessingAbortController = null
    }
  }
}