{"name": "intderserver", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/vision": "^4.3.3", "@google/generative-ai": "^0.24.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "groq-sdk": "^0.15.0", "interview-coder-v1": "file:..", "node-fetch": "^2.7.0", "tesseract.js": "^6.0.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/node": "^22.13.5", "nodemon": "^3.1.10", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2"}, "description": ""}