const express = require("express");
const cors = require("cors");
const { GoogleGenerativeAI } = require("@google/generative-ai");
const dotenv = require("dotenv");
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");
const fetch = require("node-fetch");

// Load environment variables from .env file
dotenv.config();

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI("AIzaSyCZ-xmjwixxSBj_6ZMczjlR4t1qI-TXTZA");

const app = express();
const port = 3000;

// Increase payload size for image data
app.use(cors());
app.use(express.json({ limit: "50mb" }));

// Create temp directory if it doesn't exist
const tempDir = path.join(__dirname, "temp");
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir);
}

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Initialize OpenRouter API configuration
const apiKey = "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";
console.log(`Using OpenRouter API key: ${apiKey.substring(0, 10)}...`);

// Simple JSON parser for programming languages
function simpleJsonParse(responseText, language) {
  console.log("Using simple JSON parser for programming language:", language);
  console.log("Response preview:", responseText.substring(0, 200) + (responseText.length > 200 ? "..." : ""));

  // Strategy 1: Try to parse the entire response as JSON
  try {
    const parsed = JSON.parse(responseText);
    console.log("Successfully parsed entire response as JSON");
    return { success: true, data: parsed };
  } catch (error) {
    console.log("Failed to parse entire response as JSON:", error.message);
  }

  // Strategy 2: Extract JSON from markdown code blocks
  try {
    const codeBlockMatch = responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (codeBlockMatch) {
      console.log("Found JSON in markdown code block");
      const jsonStr = codeBlockMatch[1];
      const parsed = JSON.parse(jsonStr);
      console.log("Successfully parsed JSON from code block");
      return { success: true, data: parsed };
    }
  } catch (error) {
    console.log("Failed to parse JSON from code block:", error.message);
  }

  // Strategy 3: Find JSON object boundaries
  try {
    const jsonMatch = responseText.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0]);
      console.log("Successfully parsed JSON object");
      return { success: true, data: parsed };
    }
  } catch (error) {
    console.log("Failed to parse JSON object:", error.message);
  }

  // Strategy 4: Simple regex extraction for programming languages
  try {
    console.log("Attempting simple field extraction for programming language...");
    const result = {};

    // Extract basic fields for programming languages with more flexible patterns
    // Handle both quoted strings and code blocks
    let codeMatch = responseText.match(/"code":\s*"((?:[^"\\]|\\.)*)"/s);
    if (!codeMatch) {
      // Try with backticks
      codeMatch = responseText.match(/"code":\s*`([^`]*)`/s);
    }
    if (!codeMatch) {
      // Try to find code in markdown code blocks
      codeMatch = responseText.match(/```(?:java|javascript|python|cpp|c\+\+|c)?\s*\n([\s\S]*?)```/);
    }
    if (!codeMatch) {
      // Try to find any code block
      codeMatch = responseText.match(/```[\w]*\n([\s\S]*?)```/);
    }

    if (codeMatch) {
      result.code = codeMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n').replace(/\\t/g, '\t').trim();
    }

    // Extract time complexity with more flexible patterns
    let timeComplexityMatch = responseText.match(/"time_complexity":\s*"([^"]*(?:\\.[^"]*)*)"/);
    if (!timeComplexityMatch) {
      // Try to find O() notation anywhere in the text
      timeComplexityMatch = responseText.match(/time.*?complexity.*?[:\-]\s*(O\([^)]+\))/i);
      if (!timeComplexityMatch) {
        timeComplexityMatch = responseText.match(/(O\([^)]+\)).*?time/i);
      }
    }
    if (timeComplexityMatch) {
      result.time_complexity = timeComplexityMatch[1].replace(/\\"/g, '"').trim();
    }

    // Extract space complexity with more flexible patterns
    let spaceComplexityMatch = responseText.match(/"space_complexity":\s*"([^"]*(?:\\.[^"]*)*)"/);
    if (!spaceComplexityMatch) {
      // Try to find O() notation anywhere in the text
      spaceComplexityMatch = responseText.match(/space.*?complexity.*?[:\-]\s*(O\([^)]+\))/i);
      if (!spaceComplexityMatch) {
        spaceComplexityMatch = responseText.match(/(O\([^)]+\)).*?space/i);
      }
    }
    if (spaceComplexityMatch) {
      result.space_complexity = spaceComplexityMatch[1].replace(/\\"/g, '"').trim();
    }

    // Extract thoughts array with more flexible pattern
    const thoughtsMatch = responseText.match(/"thoughts":\s*\[([\s\S]*?)(?:\]|$)/);
    if (thoughtsMatch) {
      try {
        let thoughtsStr = thoughtsMatch[1];
        thoughtsStr = thoughtsStr.replace(/\s*\]\s*$/, ''); // Remove trailing ]

        const thoughtsArray = [];
        const thoughtMatches = thoughtsStr.match(/"([^"]*(?:\\.[^"]*)*)"/g);

        if (thoughtMatches) {
          thoughtMatches.forEach(match => {
            const thought = match.slice(1, -1)
              .replace(/\\"/g, '"')
              .replace(/\\n/g, '\n')
              .trim();
            if (thought) {
              thoughtsArray.push(thought);
            }
          });
        }

        result.thoughts = thoughtsArray.slice(0, 4);
        if (result.thoughts.length === 0) {
          result.thoughts = ["Could not parse thoughts"];
        }
      } catch (error) {
        console.log("Error parsing thoughts:", error.message);
        result.thoughts = ["Could not parse thoughts"];
      }
    }

    // Ensure we have all required fields with defaults
    if (!result.code) {
      // Try to extract any code-like content
      const anyCodeMatch = responseText.match(/```[\s\S]*?```/);
      if (anyCodeMatch) {
        result.code = anyCodeMatch[0].replace(/```[\w]*\n?/g, '').replace(/```/g, '').trim();
      } else {
        result.code = responseText.trim();
      }
    }
    if (!result.thoughts) {
      result.thoughts = ["Extracted from response"];
    }
    if (!result.time_complexity) {
      result.time_complexity = "Not specified";
    }
    if (!result.space_complexity) {
      result.space_complexity = "Not specified";
    }

    console.log("Successfully extracted fields for programming language:", {
      codeLength: result.code.length,
      thoughtsCount: result.thoughts.length,
      hasTimeComplexity: !!result.time_complexity,
      hasSpaceComplexity: !!result.space_complexity
    });
    return { success: true, data: result };
  } catch (error) {
    console.log("Failed simple field extraction:", error.message);
  }

  console.log("Simple JSON parsing failed, returning basic structure");
  // Always return a valid structure for programming languages
  return {
    success: true,
    data: {
      code: responseText.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || responseText,
      thoughts: ["Extracted from response"],
      time_complexity: "Not specified",
      space_complexity: "Not specified"
    }
  };
}

// Utility function to safely parse JSON from model responses
function safeJsonParse(responseText, language = null) {
  console.log("Attempting to parse JSON from response...");
  console.log("Response preview:", responseText.substring(0, 200) + (responseText.length > 200 ? "..." : ""));

  // For programming languages, use simple parser
  const programmingLanguages = ["java", "javascript", "python", "c++", "cpp", "c", "go", "rust", "swift", "kotlin"];
  if (language && programmingLanguages.includes(language.toLowerCase())) {
    return simpleJsonParse(responseText, language);
  }

  // For other languages (MCQ, plaintext, SQL), use the complex parser
  // Strategy 1: Try to parse the entire response as JSON
  try {
    const parsed = JSON.parse(responseText);
    console.log("Successfully parsed entire response as JSON");
    return { success: true, data: parsed };
  } catch (error) {
    console.log("Failed to parse entire response as JSON:", error.message);
  }

  // Strategy 2: Extract JSON from markdown code blocks
  try {
    // Look for JSON inside markdown code blocks
    const codeBlockMatch = responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
    if (codeBlockMatch) {
      console.log("Found JSON in markdown code block");
      const jsonStr = codeBlockMatch[1];
      const parsed = JSON.parse(jsonStr);
      console.log("Successfully parsed JSON from code block");
      return { success: true, data: parsed };
    }
  } catch (error) {
    console.log("Failed to parse JSON from code block:", error.message);
  }

  // Strategy 3: Find JSON boundaries and parse
  try {
    // Look for JSON object boundaries with better regex
    const jsonMatches = responseText.match(/\{(?:[^{}]|{[^{}]*})*\}/g);

    if (jsonMatches && jsonMatches.length > 0) {
      // Try each potential JSON match
      for (let i = 0; i < jsonMatches.length; i++) {
        try {
          const parsed = JSON.parse(jsonMatches[i]);
          console.log(`Successfully parsed JSON match ${i + 1}`);
          return { success: true, data: parsed };
        } catch (error) {
          console.log(`Failed to parse JSON match ${i + 1}:`, error.message);
          continue;
        }
      }
    }
  } catch (error) {
    console.log("Failed to extract JSON with regex:", error.message);
  }

  // Strategy 4: Extract key-value pairs manually for non-programming languages
  try {
    console.log("Attempting manual key-value extraction...");
    const result = {};

    // Extract common fields using regex - more robust patterns
    const patterns = {
      question_text: /"question_text":\s*"([^"]*(?:\\.[^"]*)*)"/,
      correct_answer: /"correct_answer":\s*"([^"]*(?:\\.[^"]*)*)"/,
      explanation: /"explanation":\s*"([^"]*(?:\\.[^"]*)*)"/s,
      code: /"code":\s*"([^"]*(?:\\.[^"]*)*)"/s,
      thoughts: /"thoughts":\s*\[([\s\S]*?)(?:\]|$)/,
      time_complexity: /"time_complexity":\s*"([^"]*(?:\\.[^"]*)*)"/,
      space_complexity: /"space_complexity":\s*"([^"]*(?:\\.[^"]*)*)"/,
      query_complexity: /"query_complexity":\s*"([^"]*(?:\\.[^"]*)*)"/
    };

    for (const [key, pattern] of Object.entries(patterns)) {
      const match = responseText.match(pattern);
      if (match) {
        if (key === 'thoughts') {
          // Parse thoughts array - handle multiline content and incomplete arrays
          try {
            let thoughtsStr = match[1];
            console.log("Raw thoughts string:", thoughtsStr.substring(0, 200) + "...");

            // Clean up the thoughts string
            thoughtsStr = thoughtsStr.replace(/\s*\]\s*$/, ''); // Remove trailing ]

            // Extract individual thoughts using a more robust approach
            const thoughtsArray = [];
            const thoughtMatches = thoughtsStr.match(/"([^"]*(?:\\.[^"]*)*)"/g);

            if (thoughtMatches) {
              thoughtMatches.forEach(match => {
                const thought = match.slice(1, -1) // Remove surrounding quotes
                  .replace(/\\"/g, '"')
                  .replace(/\\n/g, '\n')
                  .trim();
                if (thought) {
                  thoughtsArray.push(thought);
                }
              });
            }

            // Limit to 4 thoughts as per requirements
            result[key] = thoughtsArray.slice(0, 4);
            if (result[key].length === 0) {
              result[key] = ["Could not parse thoughts"];
            }
            console.log("Parsed thoughts array:", result[key]);
          } catch (error) {
            console.log("Error parsing thoughts:", error.message);
            result[key] = ["Could not parse thoughts"];
          }
        } else {
          result[key] = match[1].replace(/\\"/g, '"').replace(/\\n/g, '\n');
        }
      }
    }

    if (Object.keys(result).length > 0) {
      console.log("Successfully extracted key-value pairs manually");
      return { success: true, data: result };
    }
  } catch (error) {
    console.log("Failed manual key-value extraction:", error.message);
  }

  console.log("All JSON parsing strategies failed");
  return { success: false, error: "Could not parse JSON from response" };
}

// OpenRouter API function with timeout and retry logic
async function callOpenRouterAPI(messages, isPlainText = false, retryCount = 0) {
  const maxRetries = 2;
  const timeoutMs = 360000; // 180 seconds timeout

  try {
    console.log(`Making OpenRouter API call (attempt ${retryCount + 1}/${maxRetries + 1})...`);

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': 'http://localhost:3000', // Your site URL
        'X-Title': 'Interview Coder', // Your l̥l̥l̥l̥l̥l̥site name
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'qwen/qwq-32b:free',
        messages: messages,
        temperature: isPlainText ? 0.7 : 0.6,
        max_tokens:  15000,
        top_p: 0.95,
        stream: false,
      }),
      signal: controller.signal,
      timeout: timeoutMs,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`OpenRouter API error: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('..............OpenRouter API call successful',result);
    return result;

  } catch (error) {
    console.error(`OpenRouter API call failed (attempt ${retryCount + 1}):`, error.message);

    // Check if it's a timeout or network error and we can retry
    if ((error.name === 'AbortError' || error.code === 'ETIMEDOUT' || error.message.includes('timeout')) && retryCount < maxRetries) {
      console.log(`Retrying OpenRouter API call in 2 seconds...`);
      await new Promise(resolve => setTimeout(resolve, 2000));
      return callOpenRouterAPI(messages, isPlainText, retryCount + 1);
    }

    // If we've exhausted retries or it's a different error, throw it
    throw error;
  }
}

// Function to convert base64 image to format suitable for Gemini
const prepareImageForGemini = (base64Data) => {
  // Remove the data URL prefix if present (e.g., "data:image/png;base64,")
  let imageData = base64Data;
  if (base64Data.includes(';base64,')) {
    imageData = base64Data.split(';base64,').pop();
  }

  return {
    inlineData: {
      data: imageData,
      mimeType: "image/png"
    }
  };
};

// --- Gemini Text Extraction Endpoint ---
app.post("/api/extract", async (req, res) => {
  console.log("===============Extract endpoint called with:===============", req.body);

  try {
    const { imageDataList, language } = req.body;
    if (!imageDataList || !Array.isArray(imageDataList)) {
      return res.status(400).json({ error: "Invalid imageDataList" });
    }

    console.log(`Processing ${imageDataList.length} images with Gemini API`);

    const extractionResults = [];

    // Get the Gemini model
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash-exp" });

    // Process each image
    for (let i = 0; i < imageDataList.length; i++) {
      const imageData = imageDataList[i];
      console.log(`Processing image ${i + 1}/${imageDataList.length}`);

      // Prepare image for Gemini
      const imagePart = prepareImageForGemini(imageData);

      // Create prompt for text extraction
      const prompt = "Extract all text from this image. Return only the text content, preserving the original formatting and structure. Do not add any explanations or comments.";

      try {
        // Generate content using Gemini
        const result = await model.generateContent([prompt, imagePart]);
        const response = await result.response;
        const extractedText = response.text();

        extractionResults.push(extractedText);
        console.log(`Extracted text from image ${i + 1}:`, extractedText.substring(0, 100) + (extractedText.length > 100 ? "..." : ""));
      } catch (imageError) {
        console.error(`Error processing image ${i + 1}:`, imageError);
        extractionResults.push(""); // Add empty string for failed extractions
      }
    }

    const problemText = extractionResults.join("\n");
    console.log("Gemini extraction successful. Total extracted text:", problemText.substring(0, 100) + (problemText.length > 100 ? "..." : ""));

    res.json({ problemText });
  } catch (error) {
    console.error("Gemini extraction error:", error);
    res.status(500).json({ error: "Text extraction failed", details: error.message });
  }
});

// --- Generation Endpoint using Groq for Chat Completions ---
// Modify the /api/generate endpoint in your server (paste-2.txt)
app.post("/api/generate", async (req, res) => {
  try {
    const { problemText, language } = req.body;
    console.log("===============Generate endpoint called with:===============", {
      languageRequested: language,
      problemTextLength: problemText ? problemText.length : 0,
      problemTextPreview: problemText ? problemText.substring(0, 100) + (problemText.length > 100 ? "..." : "") : "none"
    });

    if (!problemText) {
      return res.status(400).json({ error: "No problem text provided" });
    }

    const lang = language || "java";
    console.log(`Generating solution in ${lang}`);

    // Check if this is an SQL query, MCQ question, or plain text
    const isSqlQuery = lang === "sql";
    const isMcqQuestion = lang === "mcq";
    const isPlainText = lang === "plaintext";

    // Construct the message for the chat completion with specific formatting instructions
    const messages = [
      {
        role: "user",
        content: isMcqQuestion
          ? `Analyze the following multiple choice question:\n${problemText}\n
            Provide your response in the following JSON format:
            {
              "question_text": "The full text of the question and all options",
              "correct_answer": "The letter/number of the correct option (e.g., A, B, C, D, or 1, 2, 3, 4)",
              "explanation": "Detailed explanation of why this is the correct answer"
            }

            IMPORTANT:
            1. Include the complete question text and all options in the question_text field.
            2. Provide only the letter/number of the correct answer in the correct_answer field.
            3. Give a detailed explanation of why this answer is correct in the explanation field.
            4. The response MUST be valid JSON. Make sure to escape any special characters in strings properly.`
          : isPlainText
            ? `Analyze and provide an extremely detailed, comprehensive explanation for the following problem or question:\n${problemText}\n

              Provide your response in this EXACT JSON format (no markdown, no code blocks):
              {
                "thoughts": ["initial impression 1", "initial impression 2", "initial impression 3", "initial impression 4"],
                "explanation": "Your detailed explanation here as a single string with numbered points"
              }

              CRITICAL INSTRUCTIONS FOR THOUGHTS:
              The "thoughts" array must contain exactly 4 initial impressions that represent your FIRST REACTION when encountering this question - the immediate insights that come to mind before diving into detailed analysis. These should be:

              1. Your immediate understanding of what the question is asking
              2. Initial identification of the key concepts or approach needed
              3. Recognition of the main challenge or complexity in the problem
              4. Your starting strategy or first step toward solving it

              Think of these as what you would say to an interviewer in the first 30 seconds: "When I first read this, I notice..." or "My initial thought is..." These thoughts should establish the foundation that your comprehensive explanation will build upon.

              EXPLANATION WRITING STYLE:
              Write the comprehensive explanation as if you are speaking directly to a technical interviewer. Use:
              - ** Explanation contain atleast 10-12 points**
              - **Plain language**: Avoid jargon, technical acronyms, or overly complex terminology unless necessary
              - **Conversational tone**: Write as if you're verbally explaining your thought process during an interview
              - **Logical flow**: Each section should build upon the initial thoughts, creating a coherent narrative
              - **Interview-appropriate depth**: Provide thorough understanding while remaining concise and focused
              - **Clear structure**: Organize information so it's easy for an interviewer to follow your reasoning
              - **Practical examples**: Include concrete examples or scenarios when they help illustrate your points
              - **Problem-solving focus**: Emphasize your reasoning process and analytical approach, not just facts

              The explanation should demonstrate both your understanding of the subject matter and your ability to communicate complex concepts clearly in an interview setting.

              FORMATTING REQUIREMENTS:
              1Try to give real world scenario example for each subheading for the interviewer to understand the problem better
              2. Return ONLY valid JSON, no markdown formatting or code blocks
              3. Each thought should be 1-2 sentences maximum, capturing immediate analytical insights
              4. Thoughts should demonstrate interview-appropriate problem-solving intuition
              5. The explanation should expand on the initial thoughts using clear, conversational language
              6. Use \\n for line breaks within the explanation string
              7. Escape all quotes and special characters properly
              8. Create a logical flow from initial thoughts to detailed explanation that sounds natural and professional`
          : isSqlQuery
            ? `Analyze and solve the following SQL problem:\n${problemText}\n
              Provide your solution in the following JSON format:
              {
                "code": "your complete SQL query solution here",
                "thoughts": ["initial approach 1", "initial approach 2", "initial approach 3", "initial approach 4"],
                "explanation": "Detailed explanation of the SQL query and how it solves the problem",
              }

              IMPORTANT:
              1. The "thoughts" array should contain exactly 4 concise initial approaches that demonstrate your problem-solving process for this SQL challenge
              2. Each thought should represent a key insight or consideration you would share with an interviewer when first analyzing this SQL problem
              3. Focus on interview-appropriate reasoning: data analysis, query structure, performance considerations, and solution approach
              4. The response MUST be valid JSON. Make sure to escape any special characters in strings properly.`
            : `Solve the following problem in ${lang}:\n${problemText}\n
              Provide your solution in the following JSON format:
              {
                "code": "your complete code solution here",
                "thoughts": ["initial approach 1", "initial approach 2", "initial approach 3", "initial approach 4"],
                "time_complexity": "Time complexity analysis (e.g., O(n), O(log n), etc.)",
                "space_complexity": "Space complexity analysis (e.g., O(1), O(n), etc.)"
              }

              IMPORTANT:
              1. The "thoughts" array should contain exactly 4 concise initial approaches that demonstrate your problem-solving process for this coding challenge
              2. Each thought should represent a key insight or consideration you would share with an interviewer when first analyzing this problem
              3. Focus on interview-appropriate reasoning: problem understanding, algorithm choice, edge cases, and implementation approach
              4. Provide clear time and space complexity analysis
              5. The response MUST be valid JSON. Make sure to escape any special characters in strings properly.`,
      },
    ];
    console.log("Calling OpenRouter API for generation...", {
      language: lang,
      isPlainText,
      messageLength: messages[0].content.length
    });

    // Create a chat completion with streaming disabled to get full response
    // Use different parameters for plain text mode to get more detailed responses
    const chatCompletion = await callOpenRouterAPI(messages, isPlainText);

    console.log("OpenRouter API generation call successful  and getting  data");

    // Get the full response
    const fullResponse = chatCompletion.choices[0].message.content;
    console.log("Full API response getting data ");
    console.log("Response finish_reason:", chatCompletion.choices[0].finish_reason);
    console.log("Response length:", fullResponse.length);

    // Extract JSON from the response using the safe parser
    let jsonResponse;
    console.log("...................Full API response getting from model", fullResponse.substring(0, 500) + (fullResponse.length > 500 ? "..." : ""));


    try {
      const parseResult = safeJsonParse(fullResponse, language);
      if (parseResult.success) {
        jsonResponse = parseResult.data;
        console.log("......................Successfully parsed JSON response:", JSON.stringify(jsonResponse, null, 2));
      } else {
        console.log("...................JSON parsing failed, will use fallback logic");
        // Don't throw error here, let it fall through to the catch block for fallback processing
        throw new Error("JSON_PARSE_FAILED");
      }

      // Check if this is an SQL query, MCQ question, or plain text
      const isSqlQuery = language === "sql";
      const isMcqQuestion = language === "mcq";
      const isPlainText = language === "plaintext";

      // Ensure required fields exist based on query type
      if (isMcqQuestion) {
        // For MCQ, return only the required fields without thoughts, time_complexity, etc.
        jsonResponse = {
          question_text: jsonResponse.question_text || "",
          correct_answer: jsonResponse.correct_answer || "Could not determine the correct answer",
          explanation: jsonResponse.explanation || "No detailed explanation provided"
        };
      } else if (isPlainText) {
        console.log("...................Processing plain text response");
        // Process the explanation to ensure proper formatting
        let explanation = jsonResponse.explanation || "No detailed explanation provided";
        console.log(".............................Raw explanation:", explanation, "Type:", typeof explanation);

        // Ensure explanation is a string
        if (typeof explanation !== 'string') {
          explanation = String(explanation);
        }

        // Ensure proper line breaks for bullet points and numbered lists
        explanation = explanation
          .replace(/(\d+\.\s|\•\s)/g, '\n$1') // Add line break before each bullet or numbered point
          .replace(/\n\n+/g, '\n\n') // Remove excessive line breaks
          .replace(/(\d+\.\s.*?)(\d+\.)/g, '$1\n\n$2') // Ensure double line breaks between numbered points
          .replace(/(\d+\.\s.*?)(\•)/g, '$1\n\n$2') // Ensure double line breaks between numbered and bullet points
          .trim(); // Trim extra whitespace
        // Add emphasis to subheadings (lines that end with a colon)
        explanation = explanation.replace(/^(\d+\.\s.*?):$/gm, '$1:');

        // Collect more thoughts if available
        let thoughts = [];
        if (Array.isArray(jsonResponse.thoughts)) {
          thoughts = jsonResponse.thoughts;
        } else {
          // Extract key points from the explanation as thoughts
          const pointMatches = explanation.match(/\d+\.\s(.*?)(?=\n\n|\n\d+\.|\n\•|$)/g);
          if (pointMatches && pointMatches.length > 0) {
            // Take the first 4 points as thoughts
            thoughts = pointMatches.slice(0, 4).map(point => {
              // Extract just the text after the number
              const textMatch = point.match(/\d+\.\s(.*)/);
              return textMatch ? textMatch[1].trim() : point.trim();
            });
          } else {
            thoughts = ["No specific thoughts provided"];
          }
        }

        jsonResponse = {
          code: explanation,
          thoughts: thoughts
        };
      } else if (isSqlQuery) {
        jsonResponse = {
          code: jsonResponse.code || "",
          thoughts: Array.isArray(jsonResponse.thoughts) ? jsonResponse.thoughts : ["No specific thoughts provided"],
          explanation: jsonResponse.explanation || "No detailed explanation provided",
          query_complexity: jsonResponse.query_complexity || "Query complexity not specified"
        };
      } else {
        jsonResponse = {
          code: jsonResponse.code || "",
          thoughts: Array.isArray(jsonResponse.thoughts) ? jsonResponse.thoughts : ["No specific thoughts provided"],
          time_complexity: jsonResponse.time_complexity || "Not specified",
          space_complexity: jsonResponse.space_complexity || "Not specified"
        };
      }

      res.json(jsonResponse);
    } catch (error) {
      console.error("Error parsing JSON from model response:", error);

      // Check if this is an SQL query, MCQ question, or plain text
      const isSqlQuery = language === "sql";
      const isMcqQuestion = language === "mcq";
      const isPlainText = language === "plaintext";

      // Fallback: If JSON parsing fails, create a structured response from text
      let fallbackResponse;

      if (isMcqQuestion) {
        // Try to extract MCQ data from the text response
        let extractedText = fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse;

        // Try to parse structured MCQ data from the text
        let questionText = extractedText;
        let correctAnswer = "Could not determine the correct answer from the response";
        let explanation = "Could not extract a detailed explanation from the response";

        // Look for patterns that might indicate structured MCQ data
        // Pattern 1: Look for JSON-like structure in the text
        const jsonPattern = /\{[\s\S]*"question_text"[\s\S]*"correct_answer"[\s\S]*"explanation"[\s\S]*\}/;
        const jsonMatch = extractedText.match(jsonPattern);

        if (jsonMatch) {
          try {
            // Try to extract individual fields from the JSON-like text
            const questionMatch = extractedText.match(/"question_text":\s*"([^"]*(?:\\.[^"]*)*)"/);
            const answerMatch = extractedText.match(/"correct_answer":\s*"([^"]*(?:\\.[^"]*)*)"/);
            const explanationMatch = extractedText.match(/"explanation":\s*"([^"]*(?:\\.[^"]*)*)"/);

            if (questionMatch) questionText = questionMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n');
            if (answerMatch) correctAnswer = answerMatch[1].replace(/\\"/g, '"');
            if (explanationMatch) explanation = explanationMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n');
          } catch (error) {
            console.log("Failed to extract structured MCQ data from text");
          }
        }

        // Pattern 2: Look for natural language patterns
        if (questionText === extractedText) {
          // Try to extract question and answer from natural language
          const lines = extractedText.split('\n').filter(line => line.trim());

          // Look for answer patterns like "Answer: D" or "Correct answer: D"
          const answerPattern = /(?:answer|correct answer):\s*([A-D]|\d+)/i;
          for (const line of lines) {
            const match = line.match(answerPattern);
            if (match) {
              correctAnswer = match[1].toUpperCase();
              break;
            }
          }

          // Use the full text as question for now
          questionText = extractedText;
          explanation = "Extracted from model response";
        }

        fallbackResponse = {
          question_text: questionText,
          correct_answer: correctAnswer,
          explanation: explanation
        };
      } else if (isPlainText) {
        // For plain text, use the entire response as the explanation
        let explanation = fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse;

        // Try to format the text into numbered points if it's not already formatted
        if (!explanation.includes('• ') && !explanation.match(/^\d+\.\s/)) {
          // First, try to identify any natural sections in the text
          const sections = explanation.split(/(?:\r?\n){2,}/);

          if (sections.length > 1) {
            // If we have natural sections, format each as a numbered point
            explanation = sections.map((section, i) => `${i+1}. ${section.trim().replace(/\n/g, ' ')}`).join('\n\n');
          } else {
            // Split by paragraphs
            const paragraphs = explanation.split(/\n\n|\r\n\r\n|\n\r\n/);
            if (paragraphs.length > 1) {
              explanation = paragraphs.map((p, i) => `${i+1}. ${p.trim()}`).join('\n\n');
            } else {
              // Split by sentences for shorter texts
              const sentences = explanation.split(/(?<=[.!?])\s+/);
              if (sentences.length > 1) {
                // Group sentences into logical chunks of 2-3 sentences per point
                const chunks = [];
                for (let i = 0; i < sentences.length; i += 2) {
                  const chunk = sentences.slice(i, i + 2).join(' ');
                  chunks.push(chunk);
                }
                explanation = chunks.map((chunk, i) => `${i+1}. ${chunk.trim()}`).join('\n\n');
              }
            }
          }
        }

        // Ensure proper spacing between points
        explanation = explanation
          .replace(/(\d+\.\s.*?)(\d+\.)/g, '$1\n\n$2')
          .replace(/(\d+\.\s.*?)(\•)/g, '$1\n\n$2');

        // Extract key points as thoughts
        let thoughts = ["Automatically extracted from unstructured response"];
        const pointMatches = explanation.match(/\d+\.\s(.*?)(?=\n\n|\n\d+\.|\n\•|$)/g);
        if (pointMatches && pointMatches.length > 0) {
          // Take the first few points as thoughts
          thoughts = pointMatches.slice(0, 5).map(point => {
            // Extract just the text after the number
            const textMatch = point.match(/\d+\.\s(.*)/);
            return textMatch ? textMatch[1].trim() : point.trim();
          });
        }

        fallbackResponse = {
          code: explanation,
          thoughts: thoughts
        };
      } else if (isSqlQuery) {
        fallbackResponse = {
          code: fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse,
          thoughts: ["Automatically extracted from unstructured response"],
          explanation: "Could not extract a detailed explanation from the response",
          query_complexity: "Could not determine query complexity from response"
        };
      } else {
        // For programming languages, use simple fallback
        const programmingLanguages = ["java", "javascript", "python", "c++", "cpp", "c", "go", "rust", "swift", "kotlin"];
        if (language && programmingLanguages.includes(language.toLowerCase())) {
          fallbackResponse = {
            code: fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse,
            thoughts: ["Automatically extracted from unstructured response"],
            time_complexity: "Could not determine from response",
            space_complexity: "Could not determine from response"
          };
        } else {
          fallbackResponse = {
            code: fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse,
            thoughts: ["Automatically extracted from unstructured response"],
            time_complexity: "Could not determine from response",
            space_complexity: "Could not determine from response"
          };
        }
      }

      res.json(fallbackResponse);
    }
  } catch (error) {
    console.error("Generation error:", {
      message: error.message,
      name: error.name,
      code: error.code,
      language: req.body.language
    });

    // Provide more specific error messages based on error type
    let errorMessage = "Generation failed";
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      errorMessage = "Request timed out. The AI service took too long to respond. Please try again.";
    } else if (error.message.includes('OpenRouter API error')) {
      errorMessage = `AI service error: ${error.message}`;
    } else if (error.message.includes('ETIMEDOUT') || error.code === 'ETIMEDOUT') {
      errorMessage = "Network timeout. Please check your internet connection and try again.";
    }

    res.status(500).json({
      error: errorMessage,
      details: error.message,
      language: req.body.language
    });
  }
});

// --- Debug Endpoint using Groq for Chat Completions ---
// Modify the /api/debug endpoint similarly
app.post("/api/debug", async (req, res) => {
  try {
    const { problemText, language } = req.body;
    console.log("Debug endpoint called with:", {
      languageRequested: language,
      problemTextLength: problemText ? problemText.length : 0,
      problemTextPreview: problemText ? problemText.substring(0, 100) + (problemText.length > 100 ? "..." : "") : "none"
    });

    if (!problemText) {
      return res.status(400).json({ error: "No problem text provided" });
    }

    const lang = language || "java";
    console.log(`Debugging problem in ${lang}`);

    // Check if this is an SQL query, MCQ question, or plain text
    const isSqlQuery = lang === "sql";
    const isMcqQuestion = lang === "mcq";
    const isPlainText = lang === "plaintext";

    const messages = [
      {
        role: "user",
        content: isMcqQuestion
          ? `Analyze the following multiple choice question:\n${problemText}\n
            Provide your response in the following JSON format:
            {
              "question_text": "The full text of the question and all options",
              "correct_answer": "The letter/number of the correct option (e.g., A, B, C, D, or 1, 2, 3, 4)",
              "explanation": "Detailed explanation of why this is the correct answer"
            }

            IMPORTANT:
            1. Include the complete question text and all options in the question_text field.
            2. Provide only the letter/number of the correct answer in the correct_answer field.
            3. Give a detailed explanation of why this answer is correct in the explanation field.
            4. The response MUST be valid JSON. Make sure to escape any special characters in strings properly.`
          : isPlainText
            ? `Analyze and provide an extremely detailed, comprehensive explanation for the following problem or question:\n${problemText}\n

              Provide your response in this EXACT JSON format (no markdown, no code blocks):
              {
                "thoughts": ["initial impression 1", "initial impression 2", "initial impression 3", "initial impression 4"],
                "explanation": "Your detailed explanation here as a single string with numbered points"
              }

              CRITICAL INSTRUCTIONS FOR THOUGHTS:
              The "thoughts" array must contain exactly 4 initial impressions that represent your FIRST REACTION when encountering this question - the immediate insights that come to mind before diving into detailed analysis. These should be:

              1. Your immediate understanding of what the question is asking
              2. Initial identification of the key concepts or approach needed
              3. Recognition of the main challenge or complexity in the problem
              4. Your starting strategy or first step toward solving it

              Think of these as what you would say to an interviewer in the first 30 seconds: "When I first read this, I notice..." or "My initial thought is..." These thoughts should establish the foundation that your comprehensive explanation will build upon.

              EXPLANATION WRITING STYLE:
              Write the comprehensive explanation as if you are speaking directly to a technical interviewer. Use:

              - **Plain language**: Avoid jargon, technical acronyms, or overly complex terminology unless necessary
              - **Conversational tone**: Write as if you're verbally explaining your thought process during an interview
              - **Logical flow**: Each section should build upon the initial thoughts, creating a coherent narrative
              - **Interview-appropriate depth**: Provide thorough understanding while remaining concise and focused
              - **Clear structure**: Organize information so it's easy for an interviewer to follow your reasoning
              - **Practical examples**: Include concrete examples or scenarios when they help illustrate your points
              - **Problem-solving focus**: Emphasize your reasoning process and analytical approach, not just facts

              The explanation should demonstrate both your understanding of the subject matter and your ability to communicate complex concepts clearly in an interview setting.

              FORMATTING REQUIREMENTS:
              1. Return ONLY valid JSON, no markdown formatting or code blocks
              2. Each thought should be 1-2 sentences maximum, capturing immediate analytical insights
              3. Thoughts should demonstrate interview-appropriate problem-solving intuition
              4. The explanation should expand on the initial thoughts using clear, conversational language
              5. Use \\n for line breaks within the explanation string
              6. Escape all quotes and special characters properly
              7. Create a logical flow from initial thoughts to detailed explanation that sounds natural and professional`
          : isSqlQuery
            ? `Debug the following SQL problem:\n${problemText}\n
              Provide your debug solution in the following JSON format:
              {
                "code": "your complete fixed SQL query solution here",
                "thoughts": ["debug observation 1", "debug observation 2", "debug observation 3", "debug observation 4"],
                "explanation": "Detailed explanation of the SQL query and how it solves the problem",
                "query_complexity": "Explanation of the query complexity and performance considerations"
              }

              IMPORTANT:
              1. The "thoughts" array should contain exactly 4 concise debug observations that demonstrate your problem-solving process for this SQL issue
              2. Each thought should represent a key insight or consideration you would share with an interviewer when debugging this SQL problem
              3. Focus on interview-appropriate debugging reasoning: error identification, solution approach, optimization considerations, and validation steps
              4. The response MUST be valid JSON. Make sure to escape any special characters in strings properly.`
            : `Debug the following problem in ${lang}:\n${problemText}\n
              Provide your debug solution in the following JSON format:
              {
                "code": "your complete fixed code solution here",
                "thoughts": ["debug observation 1", "debug observation 2", "debug observation 3", "debug observation 4"],
                "time_complexity": "Time complexity analysis (e.g., O(n), O(log n), etc.)",
                "space_complexity": "Space complexity analysis (e.g., O(1), O(n), etc.)"
              }

              IMPORTANT:
              1. The "thoughts" array should contain exactly 4 concise debug observations that demonstrate your problem-solving process for this coding issue
              2. Each thought should represent a key insight or consideration you would share with an interviewer when debugging this problem
              3. Focus on interview-appropriate debugging reasoning: error identification, solution approach, edge case handling, and validation steps
              4. Provide clear time and space complexity analysis
              5. The response MUST be valid JSON. Make sure to escape any special characters in strings properly.`,
      },
    ];

    console.log("Calling OpenRouter API for debugging...", {
      language: lang,
      isPlainText,
      messageLength: messages[0].content.length
    });

    // Use non-streaming response with adjusted parameters for plain text
    const chatCompletion = await callOpenRouterAPI(messages, isPlainText);

    console.log("OpenRouter API debug call successful");

    // Get the full response
    const fullResponse = chatCompletion.choices[0].message.content;

    // Extract JSON from the response using the safe parser
    let jsonResponse;
    try {
      const parseResult = safeJsonParse(fullResponse, language);
      if (parseResult.success) {
        jsonResponse = parseResult.data;
        console.log("......................Successfully parsed debug JSON response:", JSON.stringify(jsonResponse, null, 2));
      } else {
        console.log("...................Debug JSON parsing failed, will use fallback logic");
        // Don't throw error here, let it fall through to the catch block for fallback processing
        throw new Error("JSON_PARSE_FAILED");
      }

      // Check if this is an SQL query, MCQ question, or plain text
      const isSqlQuery = language === "sql";
      const isMcqQuestion = language === "mcq";
      const isPlainText = language === "plaintext";

      // Ensure required fields exist based on query type
      if (isMcqQuestion) {
        // For MCQ, return only the required fields without thoughts, time_complexity, etc.
        jsonResponse = {
          question_text: jsonResponse.question_text || "",
          correct_answer: jsonResponse.correct_answer || "Could not determine the correct answer",
          explanation: jsonResponse.explanation || "No detailed explanation provided"
        };
      } else if (isPlainText) {
        // Process the explanation to ensure proper formatting
        let explanation = jsonResponse.explanation || "No detailed explanation provided";

        // Ensure proper line breaks for bullet points and numbered lists
        explanation = explanation
          .replace(/(\d+\.\s|\•\s)/g, '\n$1') // Add line break before each bullet or numbered point
          .replace(/\n\n+/g, '\n\n') // Remove excessive line breaks
          .replace(/(\d+\.\s.*?)(\d+\.)/g, '$1\n\n$2') // Ensure double line breaks between numbered points
          .replace(/(\d+\.\s.*?)(\•)/g, '$1\n\n$2') // Ensure double line breaks between numbered and bullet points
          .trim(); // Trim extra whitespace

        // Add emphasis to subheadings (lines that end with a colon)
        explanation = explanation.replace(/^(\d+\.\s.*?):$/gm, '$1:');

        // Collect more thoughts if available
        let thoughts = [];
        if (Array.isArray(jsonResponse.thoughts)) {
          thoughts = jsonResponse.thoughts;
        } else {
          // Extract key points from the explanation as thoughts
          const pointMatches = explanation.match(/\d+\.\s(.*?)(?=\n\n|\n\d+\.|\n\•|$)/g);
          if (pointMatches && pointMatches.length > 0) {
            // Take the first 4 points as thoughts
            thoughts = pointMatches.slice(0, 4).map(point => {
              // Extract just the text after the number
              const textMatch = point.match(/\d+\.\s(.*)/);
              return textMatch ? textMatch[1].trim() : point.trim();
            });
          } else {
            thoughts = ["No specific debug observations provided"];
          }
        }

        jsonResponse = {
          code: explanation,
          thoughts: thoughts
        };
      } else if (isSqlQuery) {
        jsonResponse = {
          code: jsonResponse.code || "",
          thoughts: Array.isArray(jsonResponse.thoughts) ? jsonResponse.thoughts : ["No specific debug observations provided"],
          explanation: jsonResponse.explanation || "No detailed explanation provided",
          query_complexity: jsonResponse.query_complexity || "Query complexity not specified"
        };
      } else {
        jsonResponse = {
          code: jsonResponse.code || "",
          thoughts: Array.isArray(jsonResponse.thoughts) ? jsonResponse.thoughts : ["No specific debug observations provided"],
          time_complexity: jsonResponse.time_complexity || "Not specified",
          space_complexity: jsonResponse.space_complexity || "Not specified"
        };
      }

      res.json(jsonResponse);
    } catch (error) {
      console.error("Error parsing JSON from model response:", error);

      // Check if this is an SQL query, MCQ question, or plain text
      const isSqlQuery = language === "sql";
      const isMcqQuestion = language === "mcq";
      const isPlainText = language === "plaintext";

      // Fallback: If JSON parsing fails, create a structured response from text
      let fallbackResponse;

      if (isMcqQuestion) {
        // Try to extract MCQ data from the text response
        let extractedText = fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse;

        // Try to parse structured MCQ data from the text
        let questionText = extractedText;
        let correctAnswer = "Could not determine the correct answer from the response";
        let explanation = "Could not extract a detailed explanation from the response";

        // Look for patterns that might indicate structured MCQ data
        // Pattern 1: Look for JSON-like structure in the text
        const jsonPattern = /\{[\s\S]*"question_text"[\s\S]*"correct_answer"[\s\S]*"explanation"[\s\S]*\}/;
        const jsonMatch = extractedText.match(jsonPattern);

        if (jsonMatch) {
          try {
            // Try to extract individual fields from the JSON-like text
            const questionMatch = extractedText.match(/"question_text":\s*"([^"]*(?:\\.[^"]*)*)"/);
            const answerMatch = extractedText.match(/"correct_answer":\s*"([^"]*(?:\\.[^"]*)*)"/);
            const explanationMatch = extractedText.match(/"explanation":\s*"([^"]*(?:\\.[^"]*)*)"/);

            if (questionMatch) questionText = questionMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n');
            if (answerMatch) correctAnswer = answerMatch[1].replace(/\\"/g, '"');
            if (explanationMatch) explanation = explanationMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n');
          } catch (error) {
            console.log("Failed to extract structured MCQ data from debug text");
          }
        }

        // Pattern 2: Look for natural language patterns
        if (questionText === extractedText) {
          // Try to extract question and answer from natural language
          const lines = extractedText.split('\n').filter(line => line.trim());

          // Look for answer patterns like "Answer: D" or "Correct answer: D"
          const answerPattern = /(?:answer|correct answer):\s*([A-D]|\d+)/i;
          for (const line of lines) {
            const match = line.match(answerPattern);
            if (match) {
              correctAnswer = match[1].toUpperCase();
              break;
            }
          }

          // Use the full text as question for now
          questionText = extractedText;
          explanation = "Extracted from debug model response";
        }

        fallbackResponse = {
          question_text: questionText,
          correct_answer: correctAnswer,
          explanation: explanation
        };
      } else if (isPlainText) {
        // For plain text, use the entire response as the explanation
        let explanation = fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse;

        // Try to format the text into numbered points if it's not already formatted
        if (!explanation.includes('• ') && !explanation.match(/^\d+\.\s/)) {
          // First, try to identify any natural sections in the text
          const sections = explanation.split(/(?:\r?\n){2,}/);

          if (sections.length > 1) {
            // If we have natural sections, format each as a numbered point
            explanation = sections.map((section, i) => `${i+1}. ${section.trim().replace(/\n/g, ' ')}`).join('\n\n');
          } else {
            // Split by paragraphs
            const paragraphs = explanation.split(/\n\n|\r\n\r\n|\n\r\n/);
            if (paragraphs.length > 1) {
              explanation = paragraphs.map((p, i) => `${i+1}. ${p.trim()}`).join('\n\n');
            } else {
              // Split by sentences for shorter texts
              const sentences = explanation.split(/(?<=[.!?])\s+/);
              if (sentences.length > 1) {
                // Group sentences into logical chunks of 2-3 sentences per point
                const chunks = [];
                for (let i = 0; i < sentences.length; i += 2) {
                  const chunk = sentences.slice(i, i + 2).join(' ');
                  chunks.push(chunk);
                }
                explanation = chunks.map((chunk, i) => `${i+1}. ${chunk.trim()}`).join('\n\n');
              }
            }
          }
        }

        // Ensure proper spacing between points
        explanation = explanation
          .replace(/(\d+\.\s.*?)(\d+\.)/g, '$1\n\n$2')
          .replace(/(\d+\.\s.*?)(\•)/g, '$1\n\n$2');

        // Extract key points as thoughts
        let thoughts = ["Automatically extracted from unstructured debug response"];
        const pointMatches = explanation.match(/\d+\.\s(.*?)(?=\n\n|\n\d+\.|\n\•|$)/g);
        if (pointMatches && pointMatches.length > 0) {
          // Take the first few points as thoughts
          thoughts = pointMatches.slice(0, 5).map(point => {
            // Extract just the text after the number
            const textMatch = point.match(/\d+\.\s(.*)/);
            return textMatch ? textMatch[1].trim() : point.trim();
          });
        }

        fallbackResponse = {
          code: explanation,
          thoughts: thoughts
        };
      } else if (isSqlQuery) {
        fallbackResponse = {
          code: fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse,
          thoughts: ["Automatically extracted from unstructured debug response"],
          explanation: "Could not extract a detailed explanation from the response",
          query_complexity: "Could not determine query complexity from response"
        };
      } else {
        fallbackResponse = {
          code: fullResponse.replace(/```[\w]*\n([\s\S]*?)```/g, "$1").trim() || fullResponse,
          thoughts: ["Automatically extracted from unstructured debug response"],
          time_complexity: "Could not determine from response",
          space_complexity: "Could not determine from response"
        };
      }

      res.json(fallbackResponse);
    }
  } catch (error) {
    console.error("Debug error:", {
      message: error.message,
      name: error.name,
      code: error.code,
      language: req.body.language
    });

    // Provide more specific error messages based on error type
    let errorMessage = "Debug generation failed";
    if (error.name === 'AbortError' || error.message.includes('timeout')) {
      errorMessage = "Request timed out. The AI service took too long to respond. Please try again.";
    } else if (error.message.includes('OpenRouter API error')) {
      errorMessage = `AI service error: ${error.message}`;
    } else if (error.message.includes('ETIMEDOUT') || error.code === 'ETIMEDOUT') {
      errorMessage = "Network timeout. Please check your internet connection and try again.";
    }

    res.status(500).json({
      error: errorMessage,
      details: error.message,
      language: req.body.language
    });
  }
});

// Add a test endpoint to verify OpenRouter API connectivity
app.get("/api/test-openrouter", async (req, res) => {
  try {
    console.log("Testing OpenRouter API connection...");
    const completion = await callOpenRouterAPI([
      { role: "user", content: "Hello, can you respond with just the text 'OpenRouter API is working'?" }
    ]);

    console.log("OpenRouter API test response:", completion.choices[0].message.content);
    res.json({ status: "success", message: completion.choices[0].message.content });
  } catch (error) {
    console.error("OpenRouter API test error:", error);
    res.status(500).json({ status: "error", error: error.message });
  }
});

app.listen(port, () => {
  console.log(`Server listening on port ${port} at ${new Date().toISOString()}`);
  console.log(`API endpoints available:
  - POST /api/extract - Extract text from images
  - POST /api/generate - Generate solutions from problem text
  - POST /api/debug - Debug code problems
  - GET /api/test-openrouter - Test OpenRouter API connectivity`);
});

app.get("/cron", (req, res) => {
  console.log(`${new Date().toISOString()} - Cron endpoint called`);
  res.send("happy");
});
