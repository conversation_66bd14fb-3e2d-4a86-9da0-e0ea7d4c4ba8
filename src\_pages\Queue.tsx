import React, { useState, useEffect, useRef, useCallback } from "react"
import { useQuery } from "@tanstack/react-query"
import ScreenshotQueue from "../components/Queue/ScreenshotQueue"
import QueueCommands from "../components/Queue/QueueCommands"
import TextInputComponent, { TextInputComponentRef } from "../components/TextInput/TextInputComponent"
import InputModeToggle, { InputMode } from "../components/TextInput/InputModeToggle"
import VoiceInputComponent, { VoiceInputComponentRef } from "../components/VoiceInput/VoiceInputComponent"

import { useToast } from "../contexts/toast"
import { Screenshot } from "../types/screenshots"

async function fetchScreenshots(): Promise<Screenshot[]> {
  try {
    const result = await window.electronAPI.getScreenshots();
    if (result.success && result.previews) {
      return result.previews.map(p => ({
        id: p.path,
        path: p.path,
        preview: p.preview,
        timestamp: Date.now()
      }));
    }
    return [];
  } catch (error) {
    console.error("Error loading screenshots:", error);
    return [];
  }
}

interface QueueProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const Queue: React.FC<QueueProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const { showToast } = useToast()

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)
  const [inputMode, setInputMode] = useState<InputMode>("screenshot")
  const [isProcessing, setIsProcessing] = useState(false)
  const contentRef = useRef<HTMLDivElement>(null)
  const textInputRef = useRef<TextInputComponentRef>(null)
  const voiceInputRef = useRef<VoiceInputComponentRef>(null)

  const {
    data: screenshots = [],
    isLoading,
    refetch
  } = useQuery<Screenshot[]>({
    queryKey: ["screenshots"],
    queryFn: fetchScreenshots,
    staleTime: Infinity,
    gcTime: Infinity,
    refetchOnWindowFocus: false
  })

  const handleDeleteScreenshot = async (index: number) => {
    const screenshotToDelete = screenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        refetch() // Refetch screenshots instead of managing state directly
      } else {
        console.error("Failed to delete screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot file", "error")
      }
    } catch (error) {
      console.error("Error deleting screenshot:", error)
    }
  }

  // Handle text input submission
  const handleTextSubmit = async (text: string) => {
    if (!text.trim()) return;

    setIsProcessing(true);
    try {
      const result = await window.electronAPI.processTextInput(text, currentLanguage);
      if (!result.success) {
        showToast("Processing Failed", result.error || "Failed to process text input", "error");
      }
    } catch (error) {
      console.error("Error processing text input:", error);
      showToast("Processing Failed", "An unexpected error occurred", "error");
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle voice input submission
  const handleVoiceSubmit = useCallback((text: string) => {
    if (!text.trim()) return;
    handleTextSubmit(text);
  }, []);

  // No longer needed as we use setInputMode directly

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(() => refetch()),
      window.electronAPI.onResetView(() => refetch()),

      // We'll implement these event handlers if needed later



      window.electronAPI.onSolutionStart(() => {
        setIsProcessing(true);
      }),

      window.electronAPI.onSolutionSuccess(() => {
        setIsProcessing(false);
      }),

      window.electronAPI.onSolutionError((error: string) => {
        setIsProcessing(false);
        showToast(
          "Processing Failed",
          "There was an error processing your request.",
          "error"
        )
        setView("queue") // Revert to queue if processing fails
        console.error("Processing error:", error)
      }),

      window.electronAPI.onProcessingNoScreenshots(() => {
        setIsProcessing(false);
        showToast(
          "No Screenshots",
          "There are no screenshots to process.",
          "neutral"
        )
      }),

      window.electronAPI.onOutOfCredits(() => {
        setIsProcessing(false);
        showToast(
          "Out of Credits",
          "You are out of credits. Please refill at https://www.interviewcoder.co/settings.",
          "error"
        )
      }),

      // Add event listener for input mode switching
      window.electronAPI.onSwitchInputMode?.((mode: string) => {
        if (mode === "screenshot" || mode === "text" || mode === "voice") {
          setInputMode(mode as InputMode);
        }
      }) || (() => {}),

      // Add event listener for text input submission (Alt+Enter global shortcut)
      window.electronAPI.onSubmitTextInput?.(() => {
        if (inputMode === "text" && textInputRef.current && !isProcessing) {
          textInputRef.current.submit();
        }
      }) || (() => {}),

      // Add event listeners for voice input shortcuts
      window.electronAPI.onVoiceToggleRecording?.(() => {
        if (inputMode === "voice" && voiceInputRef.current && !isProcessing) {
          voiceInputRef.current.toggleRecording();
        }
      }) || (() => {}),

      window.electronAPI.onVoiceToggleEdit?.(() => {
        if (inputMode === "voice" && voiceInputRef.current && !isProcessing) {
          voiceInputRef.current.toggleEdit();
        }
      }) || (() => {}),

      window.electronAPI.onVoiceSubmit?.(() => {
        if (inputMode === "voice" && voiceInputRef.current && !isProcessing) {
          voiceInputRef.current.submit();
        }
      }) || (() => {})
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight, inputMode, isProcessing, textInputRef, voiceInputRef, refetch])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  return (
    <div ref={contentRef} className={`bg-transparent w-1/2`}>
      <div className="px-4 py-3">
        <div className="space-y-3 w-fit">
          <div className="flex justify-between items-center mb-2">
            <div className="flex gap-2">
              <InputModeToggle
                currentMode={inputMode}
                setInputMode={setInputMode}
              />
            </div>
          </div>

          {inputMode === "text" && (
            <div className="w-full">
              <TextInputComponent
                ref={textInputRef}
                currentLanguage={currentLanguage}
                onSubmit={handleTextSubmit}
                isProcessing={isProcessing}
              />
            </div>
          )}

          {inputMode === "voice" && (
            <div className="w-full">
              <VoiceInputComponent
                ref={voiceInputRef}
                onTranscriptionComplete={handleVoiceSubmit}
                isProcessing={isProcessing}
                deepgramApiKey="****************************************"
              />
            </div>
          )}

          {inputMode === "screenshot" && (
            <ScreenshotQueue
              isLoading={false}
              screenshots={screenshots}
              onDeleteScreenshot={handleDeleteScreenshot}
            />
          )}

          <QueueCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            screenshotCount={screenshots.length}
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />
        </div>
      </div>
    </div>
  )
}

export default Queue
