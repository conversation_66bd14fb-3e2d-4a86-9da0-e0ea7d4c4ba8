// Solutions.tsx
import React, { useState, useEffect, useRef } from "react"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism"

import ScreenshotQueue from "../components/Queue/ScreenshotQueue"

import { ProblemStatementData } from "../types/solutions"
import SolutionCommands from "../components/Solutions/SolutionCommands"
import Debug from "./Debug"
import { useToast } from "../contexts/toast"

export const ContentSection = ({
  title,
  content,
  isLoading
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide">
      {title}
    </h2>
    {isLoading ? (
      <div className="mt-4 flex">
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Extracting problem statement...
        </p>
      </div>
    ) : (
      <div className="text-[13px] leading-[1.4] text-gray-100 max-w-[600px]">
        {content}
      </div>
    )}
  </div>
)
const SolutionSection = ({
  title,
  content,
  isLoading,
  currentLanguage
}: {
  title: string
  content: React.ReactNode
  isLoading: boolean
  currentLanguage: string
}) => {
  const [copyStatus, setCopyStatus] = useState<'idle' | 'copied'>('idle');

  const handleCopy = () => {
    if (typeof content === 'string') {
      navigator.clipboard.writeText(content)
        .then(() => {
          setCopyStatus('copied');
          setTimeout(() => setCopyStatus('idle'), 2000);
        })
        .catch(err => {
          console.error('Failed to copy text: ', err);
        });
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h2 className="text-[13px] font-medium text-white tracking-wide">
          {title}
        </h2>
        {!isLoading && (
          <button
            onClick={handleCopy}
            className="text-xs text-white/70 hover:text-white/100 bg-white/10 hover:bg-white/20 rounded px-2 py-1 transition-colors flex items-center gap-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
            </svg>
            {copyStatus === 'copied' ? 'Copied!' : 'Copy'}
          </button>
        )}
      </div>
      {isLoading ? (
        <div className="space-y-1.5">
          <div className="mt-4 flex">
            <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
              Loading solutions...
            </p>
          </div>
        </div>
      ) : (
        <div className="w-full">
          {currentLanguage !== "plaintext" && currentLanguage!=="mcq" && (
            <SyntaxHighlighter
              showLineNumbers
              language={currentLanguage == "golang" ? "go" : currentLanguage}
              style={dracula}
              customStyle={{
                maxWidth: "100%",
                margin: 0,
                padding: "1rem",
                whiteSpace: "pre-wrap",
                wordBreak: "break-all",
                backgroundColor: "rgba(22, 27, 34, 0.5)"
              }}
              wrapLongLines={true}
            >
              {content as string}
            </SyntaxHighlighter>
          )}
          {currentLanguage === "plaintext" && (
            <div className="bg-black/40 p-4 rounded-lg">
              <div className="text-[13px] leading-[1.6] text-gray-100 whitespace-pre-line">
                {content ?
                  (() => {
                    // Basic formatting for plaintext in the solution section
                    let cleanText = String(content);

                    // Remove JSON artifacts if present
                    cleanText = cleanText
                      .replace(/^\s*[\{\[]\s*/, '')
                      .replace(/\s*[\}\]]\s*$/, '')
                      .replace(/^"(.*)"$/, '$1')
                      .replace(/\\n/g, '\n')
                      .replace(/\\"/g, '"')
                      .replace(/\\\\/g, '\\')
                      .trim();

                    return cleanText || 'No solution content available.';
                  })()
                  : 'No solution content available.'
                }
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export const ComplexitySection = ({
  timeComplexity,
  spaceComplexity,
  isLoading
}: {
  timeComplexity: string | null
  spaceComplexity: string | null
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide">
      Complexity
    </h2>
    {isLoading ? (
      <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
        Calculating complexity...
      </p>
    ) : (
      <div className="space-y-1">
        <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-gray-100">
          <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
          <div>
            <strong>Time:</strong> {timeComplexity}
          </div>
        </div>
        <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-gray-100">
          <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
          <div>
            <strong>Space:</strong> {spaceComplexity}
          </div>
        </div>
      </div>
    )}
  </div>
)

export const SqlComplexitySection = ({
  explanation,
  queryComplexity,
  isLoading
}: {
  explanation: string | null
  queryComplexity: string | null
  isLoading: boolean
}) => (
  <div className="space-y-2">
    <h2 className="text-[13px] font-medium text-white tracking-wide">
      SQL Analysis
    </h2>
    {isLoading ? (
      <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
        Analyzing SQL query...
      </p>
    ) : (
      <div className="space-y-3">
        <div className="space-y-1">
          <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-gray-100">
            <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
            <div>
              <strong>Explanation:</strong> {explanation}
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
)

export const MCQQuestionSection = ({
  questionText,
  isLoading
}: {
  questionText: string | null
  isLoading: boolean
}) => {
  console.log("MCQQuestionSection render======================:", { questionText: questionText ? questionText.substring(0, 100) + "..." : null, isLoading });

  // Function to format the question text for better display
  const formatQuestionText = (text: string | null) => {
    if (!text) return null;

    // Clean up any remaining JSON formatting
    let cleanText = text.replace(/^Here is the analysis of the multiple choice question in JSON format:\s*\n*/i, '');
    cleanText = cleanText.replace(/^Analyze the following multiple choice question:\s*\n*/i, '');

    // If the text contains options, format them properly
    if (cleanText.includes('A)') || cleanText.includes('(A)') || cleanText.includes('1.') || cleanText.includes('a)')) {
      // Split question from options
      const parts = cleanText.split(/(?=\n?[A-D]\)|(?=\n?\([A-D]\))|(?=\n?[1-4]\.)|(?=\n?[a-d]\)))/)
      if (parts.length > 1) {
        const question = parts[0].trim();
        const options = parts.slice(1).map(opt => opt.trim()).filter(opt => opt.length > 0);

        return (
          <div className="space-y-3">
            <div className="text-[13px] leading-[1.6] text-gray-100">
              {question}
            </div>
            {options.length > 0 && (
              <div className="space-y-1 ml-4">
                <div className="text-[12px] font-medium text-white/80">Options:</div>
                {options.map((option, index) => (
                  <div key={index} className="text-[13px] leading-[1.4] text-gray-100">
                    {option}
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      }
    }

    // Return formatted text with proper line breaks
    return (
      <div
        className="text-[13px] leading-[1.6] text-gray-100 whitespace-pre-line"
        dangerouslySetInnerHTML={{ __html: cleanText }}
      />
    );
  };

  return (
    <div className="space-y-2">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        Question
      </h2>
      {isLoading ? (
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Loading question...
        </p>
      ) : (
        <div className="max-w-[600px]">
          {formatQuestionText(questionText)}
        </div>
      )}
    </div>
  );
}

export const MCQAnswerSection = ({
  correctAnswer,
  explanation,
  isLoading
}: {
  correctAnswer: string | null
  explanation: string | null
  isLoading: boolean
}) => {
  console.log("MCQAnswerSection render.........................:", {
    correctAnswer,
    explanation: explanation ? explanation.substring(0, 100) + "..." : null,
    isLoading
  });

  // Function to format the explanation for better readability
  const formatExplanation = (text: string | null) => {
    if (!text) return null;

    // Clean up any unwanted prefixes
    let cleanText = text.replace(/^Here is the analysis of the multiple choice question in JSON format:\s*\n*/i, '');
    cleanText = cleanText.replace(/^To find the total time taken to fill the cistern, we need to calculate the work done by each pipe individually and then add them up\.\s*\n*/i, '');

    // Format numbered steps and bullet points
    cleanText = cleanText.replace(/(\d+\.\s)/g, '\n$1');
    cleanText = cleanText.replace(/(-\s)/g, '\n• ');

    // Clean up extra whitespace
    cleanText = cleanText.replace(/\n\s*\n/g, '\n\n').trim();

    return cleanText;
  };

  return (
    <div className="space-y-2">
      <h2 className="text-[13px] font-medium text-white tracking-wide">
        Solution
      </h2>
      {isLoading ? (
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Analyzing multiple choice question...
        </p>
      ) : (
        <div className="space-y-3">
          {correctAnswer && (
            <div className="space-y-1">
              <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-gray-100">
                <div className="w-1 h-1 rounded-full bg-green-400/80 mt-2 shrink-0" />
                <div>
                  <strong>Correct Answer:</strong> {correctAnswer}
                </div>
              </div>
            </div>
          )}
          {explanation && (
            <div className="space-y-1">
              <div className="flex items-start gap-2 text-[13px] leading-[1.4] text-gray-100">
                <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
                <div>
                  <strong>Explanation:</strong>
                </div>
              </div>
              <div
                className="text-[13px] leading-[1.6] text-gray-100 whitespace-pre-line ml-4"
                dangerouslySetInnerHTML={{ __html: formatExplanation(explanation) || '' }}
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export const PlainTextSection = ({
  explanation,
  isLoading
}: {
  explanation: string | null
  isLoading: boolean
}) => {
  // Helper function to extract sub-points from content
  const extractSubPoints = (content: string): string[] => {
    const subPoints = [];

    // Look for existing numbered lists (1., 2., etc.)
    if (content.match(/\d+\.\s/)) {
      const numberedParts = content.split(/(?=\d+\.\s)/);
      numberedParts.forEach(part => {
        const trimmed = part.trim();
        if (trimmed) {
          // Clean up the numbered point
          const cleanPart = trimmed.replace(/^\d+\.\s*/, '').trim();
          if (cleanPart) {
            subPoints.push(`   • ${cleanPart}`);
          }
        }
      });
    }
    // Look for bullet points with * or +
    else if (content.match(/[\*\+]\s/)) {
      const bulletParts = content.split(/(?=[\*\+]\s)/);
      bulletParts.forEach(part => {
        const trimmed = part.trim();
        if (trimmed) {
          const cleanPart = trimmed.replace(/^[\*\+]\s*/, '').trim();
          if (cleanPart) {
            subPoints.push(`   • ${cleanPart}`);
          }
        }
      });
    }
    // Split by sentences and create logical sub-points
    else {
      const sentences = content.split(/(?<=[.!?])\s+/);
      if (sentences.length > 3) {
        // Group sentences into chunks of 2-3 for better readability
        for (let i = 0; i < sentences.length; i += 2) {
          const chunk = sentences.slice(i, i + 2).join(' ').trim();
          if (chunk) {
            subPoints.push(`   • ${chunk}`);
          }
        }
      } else {
        // For shorter content, each sentence becomes a sub-point
        sentences.forEach(sentence => {
          const trimmed = sentence.trim();
          if (trimmed) {
            subPoints.push(`   • ${trimmed}`);
          }
        });
      }
    }

    return subPoints;
  };

  // Helper function to format content into sections with clear headings
  const formatContentIntoSections = (content: string): string => {
    // First, try to identify natural section boundaries
    const sections = [];

    // Look for patterns that indicate section headers
    const headerPatterns = [
      /^([A-Z][^.!?]*):(.*)$/gm, // "Header: content"
      /^(\d+\.\s*[A-Z][^.!?]*):(.*)$/gm, // "1. Header: content"
      /^([A-Z][A-Za-z\s]+):\s*$/gm, // "Header:" on its own line
    ];

    let foundHeaders = false;

    // Try to find and format existing headers
    for (const pattern of headerPatterns) {
      const matches = [...content.matchAll(pattern)];
      if (matches.length > 0) {
        foundHeaders = true;
        matches.forEach((match, index) => {
          const header = match[1].trim();
          const contentPart = match[2] ? match[2].trim() : '';

          if (contentPart) {
            const subPoints = extractSubPoints(contentPart);
            const formattedSection = subPoints.length > 0
              ? `${index + 1}. **${header}**\n\n${subPoints.join('\n\n')}`
              : `${index + 1}. **${header}**\n\n   • ${contentPart}`;
            sections.push(formattedSection);
          } else {
            sections.push(`${index + 1}. **${header}**`);
          }
        });
        break;
      }
    }

    // If no clear headers found, split by paragraphs and create logical sections
    if (!foundHeaders) {
      const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim());

      if (paragraphs.length > 1) {
        paragraphs.forEach((paragraph, index) => {
          const trimmed = paragraph.trim();
          if (trimmed) {
            // Try to extract a meaningful header from the first sentence
            const sentences = trimmed.split(/(?<=[.!?])\s+/);
            const firstSentence = sentences[0];
            const remainingSentences = sentences.slice(1);

            // Create a header from the first sentence (limit to reasonable length)
            let header = firstSentence.length > 50
              ? firstSentence.substring(0, 47) + '...'
              : firstSentence;

            // Remove trailing punctuation for header
            header = header.replace(/[.!?]+$/, '');

            if (remainingSentences.length > 0) {
              const subPoints = remainingSentences.map(s => `   • ${s.trim()}`);
              sections.push(`${index + 1}. **${header}**\n\n${subPoints.join('\n\n')}`);
            } else {
              sections.push(`${index + 1}. **${header}**`);
            }
          }
        });
      } else {
        // Single paragraph - split into sentences and group them
        const sentences = content.split(/(?<=[.!?])\s+/);
        if (sentences.length > 3) {
          const chunks = [];
          for (let i = 0; i < sentences.length; i += 3) {
            chunks.push(sentences.slice(i, i + 3));
          }

          chunks.forEach((chunk, index) => {
            const mainSentence = chunk[0].trim();
            const subSentences = chunk.slice(1).filter(s => s.trim());

            // Create header from main sentence
            let header = mainSentence.length > 50
              ? mainSentence.substring(0, 47) + '...'
              : mainSentence;
            header = header.replace(/[.!?]+$/, '');

            if (subSentences.length > 0) {
              const subPoints = subSentences.map(s => `   • ${s.trim()}`);
              sections.push(`${index + 1}. **${header}**\n\n${subPoints.join('\n\n')}`);
            } else {
              sections.push(`${index + 1}. **${header}**`);
            }
          });
        } else {
          // Very short content - just format as a single section
          const header = sentences[0] ? sentences[0].replace(/[.!?]+$/, '') : 'Main Point';
          const subPoints = sentences.slice(1).filter(s => s.trim()).map(s => `   • ${s.trim()}`);

          if (subPoints.length > 0) {
            sections.push(`1. **${header}**\n\n${subPoints.join('\n\n')}`);
          } else {
            sections.push(`1. **${header}**`);
          }
        }
      }
    }

    return sections.join('\n\n');
  };

  // Function to clean and format the explanation text
  const formatExplanation = (text: string | null) => {
    if (!text) return null;

    let cleanText = text;

    // Handle malformed JSON that's missing opening brace
    if (cleanText.startsWith('"thoughts"') || cleanText.startsWith('thoughts"')) {
      console.log("Detected malformed JSON missing opening brace, attempting to fix...");
      cleanText = '{' + cleanText;
      if (!cleanText.endsWith('}')) {
        cleanText = cleanText + '}';
      }
    }

    // First, try to detect if this is JSON-like content and extract the actual text
    try {
      // Check if the text looks like JSON or contains JSON-like structures
      if (cleanText.includes('"') && (cleanText.includes('{') || cleanText.includes('['))) {
        // Try to extract text from JSON-like structures
        const jsonMatch = cleanText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          try {
            const parsed = JSON.parse(jsonMatch[0]);
            console.log("Successfully parsed JSON in formatExplanation:", parsed);

            // If we have both thoughts and explanation, format them properly
            if (parsed.thoughts && parsed.explanation) {
              let formattedContent = '';

              // Add explanation content
              if (typeof parsed.explanation === 'string') {
                formattedContent = parsed.explanation;
              } else {
                formattedContent = String(parsed.explanation);
              }

              // Format as numbered points if not already formatted
              if (!formattedContent.match(/^\d+\.\s/m)) {
                // Split into logical sections and create numbered points
                const sections = formattedContent.split(/(?:\r?\n){2,}/).filter(s => s.trim());
                if (sections.length > 1) {
                  formattedContent = sections.map((section, i) => `${i + 1}. ${section.trim()}`).join('\n\n');
                } else {
                  // Split by sentences for single paragraph
                  const sentences = formattedContent.split(/(?<=[.!?])\s+/);
                  if (sentences.length > 3) {
                    const chunks = [];
                    for (let i = 0; i < sentences.length; i += 2) {
                      chunks.push(sentences.slice(i, i + 2).join(' '));
                    }
                    formattedContent = chunks.map((chunk, i) => `${i + 1}. ${chunk.trim()}`).join('\n\n');
                  }
                }
              }

              return formattedContent;
            }

            // If we only have thoughts, format them with meaningful headers
            if (parsed.thoughts && Array.isArray(parsed.thoughts)) {
              return parsed.thoughts.map((thought: string, i: number) => {
                // Try to create a meaningful header from the thought content
                const sentences = thought.trim().split(/(?<=[.!?])\s+/);
                const firstSentence = sentences[0];
                const remainingSentences = sentences.slice(1);

                let header = firstSentence.length > 50
                  ? firstSentence.substring(0, 47) + '...'
                  : firstSentence;
                header = header.replace(/[.!?]+$/, '');

                if (remainingSentences.length > 0) {
                  const subPoints = remainingSentences.map(s => `   • ${s.trim()}`);
                  return `${i + 1}. **${header}**\n\n${subPoints.join('\n\n')}`;
                } else {
                  return `${i + 1}. **${header}**`;
                }
              }).join('\n\n');
            }

            // Handle nested explanation object
            if (parsed.explanation && typeof parsed.explanation === 'object') {
              // Convert the explanation object into properly formatted sections
              const formattedSections = Object.entries(parsed.explanation)
                .map(([key, value], index) => {
                  // Format the key as a section header
                  const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());

                  // Process the content to create sub-points using helper function
                  let content = String(value).trim();
                  const subPoints = extractSubPoints(content);

                  // Create section with header and supporting points
                  const sectionHeader = `${index + 1}. **${formattedKey}**`;
                  return subPoints.length > 0 ? `${sectionHeader}\n\n${subPoints.join('\n\n')}` : sectionHeader;
                })
                .join('\n\n');

              return formattedSections;
            } else if (parsed.explanation && typeof parsed.explanation === 'string') {
              cleanText = parsed.explanation;
            } else if (parsed.code) {
              cleanText = parsed.code;
            } else if (typeof parsed === 'string') {
              cleanText = parsed;
            }
          } catch (e) {
            // If JSON parsing fails, continue with original text
          }
        }
      }
    } catch (e) {
      // Continue with original text if any error occurs
    }

    // Remove any remaining JSON-like artifacts
    cleanText = cleanText
      .replace(/^\s*[\{\[]\s*/, '') // Remove opening braces/brackets
      .replace(/\s*[\}\]]\s*$/, '') // Remove closing braces/brackets
      .replace(/^"(.*)"$/, '$1') // Remove surrounding quotes
      .replace(/\\n/g, '\n') // Convert escaped newlines to actual newlines
      .replace(/\\"/g, '"') // Convert escaped quotes
      .replace(/\\\\/g, '\\') // Convert escaped backslashes
      .trim();

    // If we already have formatted content from JSON parsing, return it
    if (cleanText.includes('**') && cleanText.includes('•')) {
      return cleanText;
    }

    // Use the enhanced formatting function for non-JSON content
    return formatContentIntoSections(cleanText);
  };

  // Function to render the explanation with proper React components instead of HTML strings
  const renderExplanation = (formattedText: string | null) => {
    if (!formattedText) return <div>No explanation available.</div>;

    // Split the text into lines for processing
    const lines = formattedText.split('\n');
    const elements: React.ReactNode[] = [];

    lines.forEach((line, index) => {
      const trimmedLine = line.trim();
      if (!trimmedLine) {
        elements.push(<br key={`br-${index}`} />);
        return;
      }

      // Skip redundant "Main Point" headers
      if (trimmedLine === "Main Point" || /^\d+\.\s*Main Point\s*$/.test(trimmedLine)) {
        return;
      }

      // Handle numbered points (main section headers)
      if (/^\d+\.\s/.test(trimmedLine)) {
        const match = trimmedLine.match(/^(\d+\.\s)(.*)$/);
        if (match) {
          const content = match[2].trim();
          // Skip if it's just "Main Point" or empty
          if (content && content !== "Main Point") {
            // Check if this is a bold header (contains **)
            if (content.includes('**')) {
              const parts = content.split(/\*\*([^*]+)\*\*/g);
              const formattedParts = parts.map((part, partIndex) => {
                if (partIndex % 2 === 1) {
                  return <span key={partIndex} className="text-white font-bold text-[14px]">{part}</span>;
                }
                return part;
              });
              elements.push(
                <div key={index} className="mb-5 mt-4 border-l-3 border-blue-400 pl-4 bg-[rgba(59,130,246,0.05)] py-2 rounded-r-md">
                  <span className="text-blue-400 font-medium text-[14px]">{match[1]}</span>
                  {formattedParts}
                </div>
              );
            } else {
              elements.push(
                <div key={index} className="mb-5 mt-4 border-l-3 border-blue-400 pl-4 bg-[rgba(59,130,246,0.05)] py-2 rounded-r-md">
                  <span className="text-blue-400 font-medium text-[14px]">{match[1]}</span>
                  <span className="text-white font-bold text-[14px]">{content}</span>
                </div>
              );
            }
          }
        }
        return;
      }

      // Handle bullet points (supporting points under sections)
      if (/^\s*•\s/.test(trimmedLine)) {
        const match = trimmedLine.match(/^(\s*)(•\s)(.*)$/);
        if (match) {
          const content = match[3].trim();
          // Skip if it's just a number (like "1." or "2.")
          if (content && !/^\d+\.\s*$/.test(content)) {
            const indent = match[1].length;
            elements.push(
              <div key={index} className={`mb-3 ${indent > 0 ? 'ml-8' : 'ml-6'} flex items-start`}>
                <span className="text-blue-400 mr-3 mt-1 text-[10px]">●</span>
                <span className="text-gray-100 text-[13px] leading-[1.6] flex-1">{content}</span>
              </div>
            );
          }
        }
        return;
      }

      // Handle bold text (markdown style)
      if (trimmedLine.includes('**')) {
        const parts = trimmedLine.split(/\*\*([^*]+)\*\*/g);
        const formattedParts = parts.map((part, partIndex) => {
          if (partIndex % 2 === 1) {
            return <strong key={partIndex} className="text-white font-semibold">{part}</strong>;
          }
          return part;
        });
        elements.push(<div key={index} className="mb-1">{formattedParts}</div>);
        return;
      }

      // Regular text - but skip if it's empty or just whitespace
      if (trimmedLine.length > 0) {
        elements.push(<div key={index} className="mb-1 text-gray-100">{trimmedLine}</div>);
      }
    });

    return <div className="space-y-1">{elements}</div>;
  };

  return (
    <div className="space-y-3">
      <h2 className="text-[14px] font-medium text-white tracking-wide flex items-center gap-2">
        <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
        Comprehensive Explanation
      </h2>
      {isLoading ? (
        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
          Generating detailed explanation...
        </p>
      ) : (
        <div className="bg-[rgba(22,27,34,0.3)] border border-[rgba(255,255,255,0.05)] rounded-lg p-4">
          <div className="space-y-4">
            <div className="text-[13px] leading-[1.7] text-gray-100">
              {renderExplanation(formatExplanation(explanation))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export interface SolutionsProps {
  setView: (view: "queue" | "solutions" | "debug") => void
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}
const Solutions: React.FC<SolutionsProps> = ({
  setView,
  credits,
  currentLanguage,
  setLanguage
}) => {
  const queryClient = useQueryClient()
  const contentRef = useRef<HTMLDivElement>(null)

  const [debugProcessing, setDebugProcessing] = useState(false)
  const [problemStatementData, setProblemStatementData] =
    useState<ProblemStatementData | null>(null)
  const [solutionData, setSolutionData] = useState<string | null>(null)
  const [thoughtsData, setThoughtsData] = useState<string[] | null>(null)
  const [timeComplexityData, setTimeComplexityData] = useState<string | null>(
    null
  )
  const [spaceComplexityData, setSpaceComplexityData] = useState<string | null>(
    null
  )
  const [explanationData, setExplanationData] = useState<string | null>(
    null
  )
  const [queryComplexityData, setQueryComplexityData] = useState<string | null>(
    null
  )
  const [correctAnswerData, setCorrectAnswerData] = useState<string | null>(
    null
  )
  const [questionTextData, setQuestionTextData] = useState<string | null>(
    null
  )

  const [isTooltipVisible, setIsTooltipVisible] = useState(false)
  const [tooltipHeight, setTooltipHeight] = useState(0)

  const [isResetting, setIsResetting] = useState(false)

  interface Screenshot {
    id: string
    path: string
    preview: string
    timestamp: number
  }

  const [extraScreenshots, setExtraScreenshots] = useState<Screenshot[]>([])

  useEffect(() => {
    const fetchScreenshots = async () => {
      try {
        const existing = await window.electronAPI.getScreenshots()
        console.log("Raw screenshot data:", existing)
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        console.log("Processed screenshots:", screenshots)
        setExtraScreenshots(screenshots)
      } catch (error) {
        console.error("Error loading extra screenshots:", error)
        setExtraScreenshots([])
      }
    }

    fetchScreenshots()
  }, [solutionData])

  const { showToast } = useToast()

  useEffect(() => {
    // Height update logic
    const updateDimensions = () => {
      if (contentRef.current) {
        let contentHeight = contentRef.current.scrollHeight
        const contentWidth = contentRef.current.scrollWidth
        if (isTooltipVisible) {
          contentHeight += tooltipHeight
        }
        window.electronAPI.updateContentDimensions({
          width: contentWidth,
          height: contentHeight
        })
      }
    }

    // Initialize resize observer
    const resizeObserver = new ResizeObserver(updateDimensions)
    if (contentRef.current) {
      resizeObserver.observe(contentRef.current)
    }
    updateDimensions()

    // Set up event listeners
    const cleanupFunctions = [
      window.electronAPI.onScreenshotTaken(async () => {
        try {
          const existing = await window.electronAPI.getScreenshots()
          const screenshots = (Array.isArray(existing) ? existing : []).map(
            (p) => ({
              id: p.path,
              path: p.path,
              preview: p.preview,
              timestamp: Date.now()
            })
          )
          setExtraScreenshots(screenshots)
        } catch (error) {
          console.error("Error loading extra screenshots:", error)
        }
      }),
      window.electronAPI.onResetView(() => {
        // Set resetting state first
        setIsResetting(true)

        // Remove queries
        queryClient.removeQueries({
          queryKey: ["solution"]
        })
        queryClient.removeQueries({
          queryKey: ["new_solution"]
        })

        // Reset screenshots
        setExtraScreenshots([])

        // After a small delay, clear the resetting state
        setTimeout(() => {
          setIsResetting(false)
        }, 0)
      }),
      window.electronAPI.onSolutionStart(() => {
        // Every time processing starts, reset relevant states
        setSolutionData(null)
        setThoughtsData(null)
        setTimeComplexityData(null)
        setSpaceComplexityData(null)
        setExplanationData(null)
        setQueryComplexityData(null)
        setCorrectAnswerData(null)
        setQuestionTextData(null)
      }),
      window.electronAPI.onProblemExtracted((data) => {
        queryClient.setQueryData(["problem_statement"], data)
      }),
      //if there was an error processing the initial solution
      window.electronAPI.onSolutionError((error: string) => {
        showToast("Processing Failed", error, "error")
        // Reset solutions in the cache (even though this shouldn't ever happen) and complexities to previous states
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string[]
          time_complexity?: string
          space_complexity?: string
          explanation?: string
          query_complexity?: string
          correct_answer?: string
          question_text?: string
        } | null

        if (!solution) {
          setView("queue")
          return
        }

        // Check if this is SQL data or MCQ data
        const isSqlData = 'explanation' in solution && 'query_complexity' in solution;
        const isMcqData = 'correct_answer' in solution && 'explanation' in solution;

        setSolutionData(solution?.code || null)
        setThoughtsData(solution?.thoughts || null)

        if (isMcqData) {
          setCorrectAnswerData(solution?.correct_answer || null)
          setExplanationData(solution?.explanation || null)
          setQuestionTextData(solution?.question_text || null)
          setTimeComplexityData(null)
          setSpaceComplexityData(null)
          setQueryComplexityData(null)
        } else if (isSqlData) {
          setExplanationData(solution?.explanation || null)
          setQueryComplexityData(solution?.query_complexity || null)
          setCorrectAnswerData(null)
        } else {
          setTimeComplexityData(solution?.time_complexity || null)
          setSpaceComplexityData(solution?.space_complexity || null)
          setExplanationData(null)
          setQueryComplexityData(null)
          setCorrectAnswerData(null)
          setQuestionTextData(null)
        }

        console.log("Processing error test test:", error)
      }),
      //when the initial solution is generated, we'll set the solution data to that
      window.electronAPI.onSolutionSuccess((data) => {
        console.log("=============onSolutionSuccess triggered with data:", data);

        if (!data) {
          console.warn("Received empty or invalid solution data")
          return
        }

        // Fix the code formatting by replacing escaped newlines with actual newlines
        const formattedCode = data.code ? data.code.replace(/\\n/g, '\n') : '';

        // Check if this is SQL data or MCQ data
        const isSqlData = 'explanation' in data && !('correct_answer' in data) && !('question_text' in data);
        const isMcqData = 'correct_answer' in data && 'explanation' in data && 'question_text' in data;

        console.log("=============Data type detection:", {
          isSqlData,
          isMcqData,
          hasCorrectAnswer: 'correct_answer' in data,
          hasExplanation: 'explanation' in data,
          hasQuestionText: 'question_text' in data,
          currentLanguage
        });

        let solutionData: any;

        if (isMcqData) {
          console.log("=============Processing MCQ data...");
          solutionData = {
            code: data.code || "", // MCQ might not have code
            thoughts: data.thoughts || [],
            correct_answer: data.correct_answer,
            explanation: data.explanation,
            question_text: data.question_text
          }

          console.log("=============MCQ solutionData created:", solutionData);

          queryClient.setQueryData(["solution"], solutionData)
          // For MCQ, we don't need to set solutionData since we use MCQ-specific fields
          setSolutionData(null) // Don't set this for MCQ
          setThoughtsData(solutionData.thoughts || null)
          setCorrectAnswerData(solutionData.correct_answer || null)
          setExplanationData(solutionData.explanation || null)
          setQuestionTextData(solutionData.question_text || null)
          setTimeComplexityData(null)
          setSpaceComplexityData(null)
          setQueryComplexityData(null)

          console.log("=============MCQ state setters called with:", {
            questionText: solutionData.question_text,
            correctAnswer: solutionData.correct_answer,
            explanation: solutionData.explanation
          })

          // Additional debugging - use setTimeout to log after state updates
          setTimeout(() => {
            console.log("=============MCQ state variables after update:", {
              questionTextData: solutionData.question_text,
              correctAnswerData: solutionData.correct_answer,
              explanationData: solutionData.explanation,
              currentLanguage
            })
          }, 100)
        } else if (isSqlData) {
          solutionData = {
            code: formattedCode, // Use the properly formatted code
            thoughts: data.thoughts,
            explanation: data.explanation,
            query_complexity: data.query_complexity
          }

          queryClient.setQueryData(["solution"], solutionData)
          setSolutionData(solutionData.code || null)
          setThoughtsData(solutionData.thoughts || null)
          setExplanationData(solutionData.explanation || null)
          setQueryComplexityData(solutionData.query_complexity || null)
          setTimeComplexityData(null)
          setSpaceComplexityData(null)
          setCorrectAnswerData(null)
          setQuestionTextData(null)
        } else {
          solutionData = {
            code: formattedCode, // Use the properly formatted code
            thoughts: data.thoughts,
            time_complexity: data.time_complexity,
            space_complexity: data.space_complexity
          }

          queryClient.setQueryData(["solution"], solutionData)
          setSolutionData(solutionData.code || null)
          setThoughtsData(solutionData.thoughts || null)
          setTimeComplexityData(solutionData.time_complexity || null)
          setSpaceComplexityData(solutionData.space_complexity || null)
          setExplanationData(null)
          setQueryComplexityData(null)
          setCorrectAnswerData(null)
          setQuestionTextData(null)
        }

        // Fetch latest screenshots when solution is successful
        const fetchScreenshots = async () => {
          try {
            const existing = await window.electronAPI.getScreenshots()
            const screenshots =
              existing.previews?.map((p) => ({
                id: p.path,
                path: p.path,
                preview: p.preview,
                timestamp: Date.now()
              })) || []
            setExtraScreenshots(screenshots)
          } catch (error) {
            console.error("Error loading extra screenshots:", error)
            setExtraScreenshots([])
          }
        }
        fetchScreenshots()
      }),

      //########################################################
      //DEBUG EVENTS
      //########################################################
      window.electronAPI.onDebugStart(() => {
        //we'll set the debug processing state to true and use that to render a little loader
        setDebugProcessing(true)
      }),
      //the first time debugging works, we'll set the view to debug and populate the cache with the data
      window.electronAPI.onDebugSuccess((data) => {
        queryClient.setQueryData(["new_solution"], data)
        setDebugProcessing(false)
      }),
      //when there was an error in the initial debugging, we'll show a toast and stop the little generating pulsing thing.
      window.electronAPI.onDebugError(() => {
        showToast(
          "Processing Failed",
          "There was an error debugging your code.",
          "error"
        )
        setDebugProcessing(false)
      }),
      window.electronAPI.onProcessingNoScreenshots(() => {
        showToast(
          "No Screenshots",
          "There are no extra screenshots to process.",
          "neutral"
        )
      }),
      window.electronAPI.onOutOfCredits(() => {
        showToast(
          "Out of Credits",
          "You are out of credits. Please refill at https://www.interviewcoder.co/settings.",
          "error"
        )
      })
    ]

    return () => {
      resizeObserver.disconnect()
      cleanupFunctions.forEach((cleanup) => cleanup())
    }
  }, [isTooltipVisible, tooltipHeight])

  useEffect(() => {
    setProblemStatementData(
      queryClient.getQueryData(["problem_statement"]) || null
    )
    setSolutionData(queryClient.getQueryData(["solution"]) || null)

    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.query.queryKey[0] === "problem_statement") {
        setProblemStatementData(
          queryClient.getQueryData(["problem_statement"]) || null
        )
      }
      if (event?.query.queryKey[0] === "solution") {
        const solution = queryClient.getQueryData(["solution"]) as {
          code: string
          thoughts: string[]
          time_complexity?: string
          space_complexity?: string
          explanation?: string
          query_complexity?: string
          correct_answer?: string
          question_text?: string
        } | null

        if (!solution) return;

        // Check if this is SQL data or MCQ data
        const isSqlData = 'explanation' in solution && !('correct_answer' in solution) && !('question_text' in solution);
        const isMcqData = 'correct_answer' in solution && 'explanation' in solution && 'question_text' in solution;

        setSolutionData(solution?.code ?? null)
        setThoughtsData(solution?.thoughts ?? null)

        if (isMcqData) {
          console.log("Query cache subscription - MCQ data detected:", solution);
          setCorrectAnswerData(solution?.correct_answer ?? null)
          setExplanationData(solution?.explanation ?? null)
          setQuestionTextData(solution?.question_text ?? null)
          setTimeComplexityData(null)
          setSpaceComplexityData(null)
          setQueryComplexityData(null)

          console.log("Query cache subscription - MCQ state set:", {
            correctAnswer: solution?.correct_answer,
            explanation: solution?.explanation,
            questionText: solution?.question_text
          });
        } else if (isSqlData) {
          setExplanationData(solution?.explanation ?? null)
          setQueryComplexityData(solution?.query_complexity ?? null)
          setTimeComplexityData(null)
          setSpaceComplexityData(null)
          setCorrectAnswerData(null)
          setQuestionTextData(null)
        } else {
          setTimeComplexityData(solution?.time_complexity ?? null)
          setSpaceComplexityData(solution?.space_complexity ?? null)
          setExplanationData(null)
          setQueryComplexityData(null)
          setCorrectAnswerData(null)
          setQuestionTextData(null)
        }
      }
    })
    return () => unsubscribe()
  }, [queryClient])

  const handleTooltipVisibilityChange = (visible: boolean, height: number) => {
    setIsTooltipVisible(visible)
    setTooltipHeight(height)
  }

  const handleDeleteExtraScreenshot = async (index: number) => {
    const screenshotToDelete = extraScreenshots[index]

    try {
      const response = await window.electronAPI.deleteScreenshot(
        screenshotToDelete.path
      )

      if (response.success) {
        // Fetch and update screenshots after successful deletion
        const existing = await window.electronAPI.getScreenshots()
        const screenshots = (Array.isArray(existing) ? existing : []).map(
          (p) => ({
            id: p.path,
            path: p.path,
            preview: p.preview,
            timestamp: Date.now()
          })
        )
        setExtraScreenshots(screenshots)
      } else {
        console.error("Failed to delete extra screenshot:", response.error)
        showToast("Error", "Failed to delete the screenshot", "error")
      }
    } catch (error) {
      console.error("Error deleting extra screenshot:", error)
      showToast("Error", "Failed to delete the screenshot", "error")
    }
  }

  return (
    <>
      {!isResetting && queryClient.getQueryData(["new_solution"]) ? (
        <Debug
          isProcessing={debugProcessing}
          setIsProcessing={setDebugProcessing}
          currentLanguage={currentLanguage}
          setLanguage={setLanguage}
        />
      ) : (
        <div ref={contentRef} className="relative space-y-3 px-4 py-3">
          {/* Conditionally render the screenshot queue if we have solution data or MCQ data */}
          {(solutionData || (currentLanguage === 'mcq' && (questionTextData || correctAnswerData || explanationData))) && (
            <div className="bg-transparent w-fit">
              <div className="pb-3">
                <div className="space-y-3 w-fit">
                  <ScreenshotQueue
                    isLoading={debugProcessing}
                    screenshots={extraScreenshots}
                    onDeleteScreenshot={handleDeleteExtraScreenshot}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Navbar of commands with the SolutionsHelper */}
          <SolutionCommands
            onTooltipVisibilityChange={handleTooltipVisibilityChange}
            isProcessing={
              !problemStatementData ||
              (currentLanguage === 'mcq'
                ? (!questionTextData && !correctAnswerData && !explanationData)
                : !solutionData
              )
            }
            extraScreenshots={extraScreenshots}
            credits={credits} //I have done : credits
            currentLanguage={currentLanguage}
            setLanguage={setLanguage}
          />

          {/* Main Content - Modified width constraints */}
          <div className="w-full text-sm text-black bg-black/60 rounded-md">
            <div className="rounded-lg overflow-hidden">
              <div className="px-4 py-3 space-y-4 max-w-full">
                {/* Show problem statement and loading for non-MCQ or when no data is available */}
                {(currentLanguage !== 'mcq' && !solutionData) ||
                 (currentLanguage === 'mcq' && !questionTextData && !correctAnswerData && !explanationData) ? (
                  <>
                    <ContentSection
                      title="Problem Statement"
                      content={problemStatementData?.problem_statement}
                      isLoading={!problemStatementData}
                    />
                    {problemStatementData && (
                      <div className="mt-4 flex">
                        <p className="text-xs bg-gradient-to-r from-gray-300 via-gray-100 to-gray-300 bg-clip-text text-transparent animate-pulse">
                          {currentLanguage === 'mcq' ? 'Analyzing multiple choice question...' : 'Generating solutions...'}
                        </p>
                      </div>
                    )}
                  </>
                ) : null}

                {/* Show content based on language type */}
                {currentLanguage === 'mcq' ? (
                  /* MCQ Content - Show when we have MCQ data */
                  (() => {
                    const hasAnyMcqData = !!(questionTextData || correctAnswerData || explanationData);
                    console.log("=============MCQ Display Check:", {
                      currentLanguage,
                      questionTextData: questionTextData ? questionTextData.substring(0, 100) + "..." : null,
                      correctAnswerData,
                      explanationData: explanationData ? explanationData.substring(0, 100) + "..." : null,
                      hasAnyData: hasAnyMcqData,
                      questionTextDataLength: questionTextData ? questionTextData.length : 0,
                      explanationDataLength: explanationData ? explanationData.length : 0
                    });

                    if (hasAnyMcqData) {
                      console.log("=============Rendering MCQ components...");
                      return (
                        <>
                          <MCQQuestionSection
                            questionText={questionTextData}
                            isLoading={!questionTextData}
                          />
                          <MCQAnswerSection
                            correctAnswer={correctAnswerData}
                            explanation={explanationData}
                            isLoading={!correctAnswerData || !explanationData}
                          />
                        </>
                      );
                    } else {
                      console.log("=============No MCQ data available for display - state variables are null/empty");
                      return null;
                    }
                  })()
                ) : (
                  /* Non-MCQ Content - Show when we have solution data */
                  solutionData && (
                    <>
                      <ContentSection
                        title="My Thoughts"
                        content={
                          thoughtsData && (
                            <div className="space-y-3">
                              <div className="space-y-1">
                                {thoughtsData.map((thought, index) => (
                                  <div
                                    key={index}
                                    className="flex items-start gap-2"
                                  >
                                    <div className="w-1 h-1 rounded-full bg-blue-400/80 mt-2 shrink-0" />
                                    <div>{thought}</div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )
                        }
                        isLoading={!thoughtsData}
                      />

                      {/* Only show Solution section for non-plaintext languages */}
                      {currentLanguage !== 'plaintext' && (
                        <SolutionSection
                          title="Solution"
                          content={solutionData}
                          isLoading={!solutionData}
                          currentLanguage={currentLanguage}
                        />
                      )}

                      {currentLanguage === 'sql' ? (
                        <SqlComplexitySection
                          explanation={explanationData}
                          queryComplexity={queryComplexityData}
                          isLoading={!explanationData || !queryComplexityData}
                        />
                      ) : currentLanguage === 'plaintext' ? (
                        <PlainTextSection
                          explanation={solutionData}
                          isLoading={!solutionData}
                        />
                      ) : (
                        <ComplexitySection
                          timeComplexity={timeComplexityData}
                          spaceComplexity={spaceComplexityData}
                          isLoading={!timeComplexityData || !spaceComplexityData}
                        />
                      )}
                    </>
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default Solutions
