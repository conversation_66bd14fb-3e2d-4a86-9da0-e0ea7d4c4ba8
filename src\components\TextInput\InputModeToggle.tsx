import React from "react";
import { But<PERSON> } from "../ui/button";
import { Keyboard, Image, Mic } from "lucide-react";

export type InputMode = "text" | "screenshot" | "voice";

interface InputModeToggleProps {
  currentMode: InputMode;
  setInputMode: (mode: InputMode) => void;
}

const InputModeToggle: React.FC<InputModeToggleProps> = ({
  currentMode,
  setInputMode
}) => {
  return (
    <div className="flex gap-1">
      <Button
        variant={currentMode === "text" ? "default" : "ghost"}
        size="sm"
        onClick={() => setInputMode("text")}
        className="flex items-center gap-1 text-xs"
        title="Switch to text input mode"
      >
        <Keyboard className="h-3 w-3" />
        <span>Text</span>
      </Button>
      
      <Button
        variant={currentMode === "screenshot" ? "default" : "ghost"}
        size="sm"
        onClick={() => setInputMode("screenshot")}
        className="flex items-center gap-1 text-xs"
        title="Switch to screenshot mode"
      >
        <Image className="h-3 w-3" />
        <span>Screenshot</span>
      </Button>
      
      <Button
        variant={currentMode === "voice" ? "default" : "ghost"}
        size="sm"
        onClick={() => setInputMode("voice")}
        className="flex items-center gap-1 text-xs"
        title="Switch to voice input mode"
      >
        <Mic className="h-3 w-3" />
        <span>Voice</span>
      </Button>
    </div>
  );
};

export default InputModeToggle;
