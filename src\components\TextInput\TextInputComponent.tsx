import React, { useState, useRef, useEffect, forwardRef, useImperative<PERSON>andle, useCallback } from "react";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { dracula } from "react-syntax-highlighter/dist/esm/styles/prism";
import { Button } from "../ui/button";
import { useToast } from "../../contexts/toast";

interface TextInputComponentProps {
  currentLanguage: string;
  onSubmit: (text: string) => void;
  isProcessing: boolean;
}

export interface TextInputComponentRef {
  submit: () => void;
}

const TextInputComponent: React.ForwardRefRenderFunction<TextInputComponentRef, TextInputComponentProps> = (props, ref) => {
  const { currentLanguage, onSubmit, isProcessing } = props;
  const [text, setText] = useState("");
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const { showToast } = useToast();

  const handleSubmit = useCallback(() => {
    if (!text.trim()) {
      showToast("Error", "Please enter some text before submitting", "error");
      return;
    }

    onSubmit(text);
  }, [text, onSubmit, showToast]);

  // Expose the submit method to parent components
  useImperativeHandle(ref, () => ({
    submit: () => {
      handleSubmit();
    }
  }));

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt+Enter to submit
      if (e.altKey && e.key === "Enter") {
        e.preventDefault(); // Prevent default browser behavior
        handleSubmit();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleSubmit]);

  // Determine language for syntax highlighting
  const getLanguage = () => {
    if (currentLanguage === "mcq" || currentLanguage === "aptitude" || currentLanguage === "plaintext") {
      return "text";
    }
    return currentLanguage === "golang" ? "go" : currentLanguage;
  };

  return (
    <div className="w-full space-y-2">
      <div className="relative">
        {/* Hidden textarea for actual input */}
        <textarea
          ref={textAreaRef}
          value={text}
          onChange={(e) => setText(e.target.value)}
          className="absolute inset-0 w-full h-full opacity-0 z-10 resize-none"
          placeholder="Enter your code or question here..."
          spellCheck={false}
        />

        {/* Syntax highlighter for display */}
        <div className="w-full min-h-[200px] max-h-[400px] overflow-auto ">
          <SyntaxHighlighter
            language={getLanguage()}
            style={dracula}
            customStyle={{
              margin: 0,
              padding: "1rem",
              minHeight: "80px",
              backgroundColor: "rgba(255, 255, 255, 0)",
              border: "1px solid rgba(255, 255, 255, 0.1)",
              borderRadius: "0.375rem"
            }}
            wrapLongLines={true}
          >
            {text || ""}
          </SyntaxHighlighter>
        </div>
      </div>

      <div className="flex justify-end">
        <Button
          onClick={handleSubmit}
          disabled={isProcessing || !text.trim()}
          className="text-xs"
          size="sm"
        >
          {isProcessing ? "Processing..." : "Submit (Alt+Enter)"}
        </Button>
      </div>
    </div>
  );
};

const ForwardedTextInputComponent = forwardRef(TextInputComponent);
ForwardedTextInputComponent.displayName = "TextInputComponent";

export default ForwardedTextInputComponent;


