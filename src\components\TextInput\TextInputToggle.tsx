import React from "react";
import { But<PERSON> } from "../ui/button";
import { Keyboard, Image } from "lucide-react";

interface TextInputToggleProps {
  isTextInputMode: boolean;
  toggleTextInputMode: () => void;
}

const TextInputToggle: React.FC<TextInputToggleProps> = ({
  isTextInputMode,
  toggleTextInputMode
}) => {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTextInputMode}
      className="flex items-center gap-1 text-xs"
      title={isTextInputMode ? "Switch to screenshot mode" : "Switch to text input mode"}
    >
      {isTextInputMode ? (
        <>
          <Image className="h-3 w-3" />
          <span>Screenshot Mode</span>
        </>
      ) : (
        <>
          <Keyboard className="h-3 w-3" />
          <span>Text Input Mode</span>
        </>
      )}
    </Button>
  );
};

export default TextInputToggle;
