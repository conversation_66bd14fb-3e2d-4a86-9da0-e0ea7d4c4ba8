import React, { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from "react";
import { But<PERSON> } from "../ui/button";
import { Volume2, StopCircle } from "lucide-react";
import { useToast } from "../../contexts/toast";

interface VoiceInputComponentProps {
  onTranscriptionComplete: (text: string) => void;
  isProcessing: boolean;
  deepgramApiKey: string; // Pass API key as prop for security
}

export interface VoiceInputComponentRef {
  toggleRecording: () => void;
  toggleEdit: () => void;
  submit: () => void;
}

const VoiceInputComponent: React.ForwardRefRenderFunction<VoiceInputComponentRef, VoiceInputComponentProps> = (props, ref) => {
  const { onTranscriptionComplete, isProcessing, deepgramApiKey="****************************************" } = props;
  const [isRecording, setIsRecording] = useState(false);
  const [transcription, setTranscription] = useState("");
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isEditing, setIsEditing] = useState(false);
  const [editedTranscription, setEditedTranscription] = useState("");
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const { showToast } = useToast();
  const socketRef = useRef<WebSocket | null>(null);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const animationFrameRef = useRef<number | null>(null);

  // Enhanced Deepgram connection with better error handling
  const createDeepgramConnection = useCallback(() => {
    if (!deepgramApiKey) {
      showToast("Error", "Deepgram API key is required", "error");
      return null;
    }

    const socket = new WebSocket('wss://api.deepgram.com/v1/listen', [
      'token',
      deepgramApiKey,
    ]);

    socket.onopen = () => {
      console.log('Deepgram WebSocket connection established');
      setConnectionStatus('connected');
      setIsTranscribing(true);

      // Enhanced configuration matching the SDK approach
      const config = {
        type: 'live_transcription',
        sample_rate: 48000, // Standard sample rate for web audio
        channels: 1,
        encoding: 'linear16', // More compatible than opus for live streaming
        language: 'en-US',
        model: 'nova-2', // Use nova-2 for better real-time performance
        tier: 'enhanced',
        version: 'latest',
        interim_results: true,
        smart_format: true,
        punctuate: true,
        diarize: false,
        ner: false,
        alternatives: 1,
        numerals: true,
        search: [],
        keywords: [],
        profanity_filter: false,
        redact: [],
        endpointing: 300, // Slightly longer for better accuracy
        utterance_end_ms: 1500,
        vad_turnoff: 1500,
        filler_words: false,
        multichannel: false
      };

      socket.send(JSON.stringify(config));
    };

    socket.onmessage = (message) => {
      try {
        const data = JSON.parse(message.data);

        // Handle different message types
        if (data.type === 'Results') {
          const result = data.channel?.alternatives?.[0];
          if (result?.transcript) {
            const transcript = result.transcript;

            if (data.is_final) {
              // Final result - append to transcription
              setTranscription(prev => {
                const cleanPrev = prev.replace(/\s*\[interim\].*$/, '').trim();
                return cleanPrev ? `${cleanPrev} ${transcript}` : transcript;
              });
            } else {
              // Interim result - show as preview
              setTranscription(prev => {
                const cleanPrev = prev.replace(/\s*\[interim\].*$/, '').trim();
                return `${cleanPrev} [interim]${transcript}`;
              });
            }
          }
        } else if (data.type === 'Metadata') {
          console.log('Deepgram metadata:', data);
        } else if (data.type === 'SpeechStarted') {
          console.log('Speech started');
        } else if (data.type === 'UtteranceEnd') {
          console.log('Utterance ended');
        }
      } catch (error) {
        console.error('Error parsing Deepgram message:', error);
      }
    };

    socket.onerror = (error) => {
      console.error('Deepgram WebSocket error:', error);
      setConnectionStatus('error');
      showToast("Error", "Transcription service connection failed", "error");
      setIsTranscribing(false);
    };

    socket.onclose = (event) => {
      console.log('Deepgram WebSocket connection closed:', event.code, event.reason);
      setConnectionStatus('disconnected');
      setIsTranscribing(false);

    };

    return socket;
  }, [deepgramApiKey, showToast]);

  // Audio level monitoring
  const startAudioLevelMonitoring = useCallback((stream: MediaStream) => {
    if (audioContextRef.current) {
      audioContextRef.current.close();
    }

    audioContextRef.current = new AudioContext();
    const audioSource = audioContextRef.current.createMediaStreamSource(stream);
    const analyser = audioContextRef.current.createAnalyser();

    analyser.fftSize = 256;
    analyser.smoothingTimeConstant = 0.8;
    audioSource.connect(analyser);
    analyserRef.current = analyser;

    const dataArray = new Uint8Array(analyser.frequencyBinCount);

    const checkAudioLevel = () => {
      if (!isRecording || !analyserRef.current) return;

      analyserRef.current.getByteFrequencyData(dataArray);

      // Calculate RMS (Root Mean Square) for more accurate volume
      let sum = 0;
      for (let i = 0; i < dataArray.length; i++) {
        sum += dataArray[i] * dataArray[i];
      }
      const rms = Math.sqrt(sum / dataArray.length);

      // Convert to percentage and apply some smoothing
      setAudioLevel(prev => {
        const newLevel = Math.min(100, Math.round(rms * 1.2));
        return prev * 0.7 + newLevel * 0.3; // Simple smoothing
      });

      animationFrameRef.current = requestAnimationFrame(checkAudioLevel);
    };

    checkAudioLevel();
  }, [isRecording]);

  // Start recording function
  const startRecording = useCallback(async () => {
    try {
      setConnectionStatus('connecting');
      setTranscription("");
      setAudioLevel(0);

      let stream: MediaStream;

      try {
        // First try: Request system audio capture via screen sharing
        // This is the most reliable method for capturing system audio
        stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            width: { ideal: 1 },
            height: { ideal: 1 },
            frameRate: { ideal: 1 }
          },
          audio: {
            channelCount: 1,
            sampleRate: 48000, // Match Deepgram config
            echoCancellation: true, // Don't cancel system audio
            noiseSuppression: true, // Don't suppress system audio
            autoGainControl: true   // Don't auto-adjust system audio
          }
        });

        // Remove video track since we only want audio
        const videoTracks = stream.getVideoTracks();
        videoTracks.forEach(track => {
          stream.removeTrack(track);
          track.stop();
        });

        // Verify we have audio tracks
        const audioTracks = stream.getAudioTracks();
        if (audioTracks.length === 0) {
          throw new Error('No audio tracks available. Please ensure "Share audio" is checked in the permission dialog.');
        }

      } catch (displayMediaError) {
        console.warn('getDisplayMedia failed, trying audio-only approach:', displayMediaError);

        // Fallback: Try audio-only capture (might work with virtual audio devices)
        try {
          stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              channelCount: 1,
              sampleRate: 48000,
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
              // Try to get system audio devices
              deviceId: 'default'
            }
          });
        } catch (userMediaError) {
          // If both methods fail, throw the original error with helpful message
          throw new Error(`System audio capture failed.`);
        }
      }

      // Start audio level monitoring
      startAudioLevelMonitoring(stream);

      // Create Deepgram connection
      const socket = createDeepgramConnection();
      if (!socket) {
        stream.getTracks().forEach(track => track.stop());
        return;
      }

      socketRef.current = socket;

      // Wait for connection to be established before starting recording
      await new Promise<void>((resolve, reject) => {
        socket.onopen = () => resolve();
        socket.onerror = () => reject(new Error('Failed to connect to Deepgram'));

        // Timeout after 5 seconds
        setTimeout(() => reject(new Error('Connection timeout')), 5000);
      });

      // Create MediaRecorder with optimal settings for Deepgram
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=pcm', // Use PCM if available, fallback to default
        audioBitsPerSecond: 128000
      });

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      // Handle audio data
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && socket.readyState === WebSocket.OPEN) {
          audioChunksRef.current.push(event.data);

          // Convert and send to Deepgram
          const reader = new FileReader();
          reader.onloadend = () => {
            const arrayBuffer = reader.result as ArrayBuffer;
            if (socket.readyState === WebSocket.OPEN) {
              socket.send(arrayBuffer);
            }
          };
          reader.readAsArrayBuffer(event.data);
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error('MediaRecorder error:', event);
        showToast("Error", "Recording error occurred", "error");
      };

      // Start recording with smaller chunks for real-time processing
      mediaRecorder.start(100); // 100ms chunks for very responsive transcription
      setIsRecording(true);

    } catch (error) {
      console.error("Error starting recording:", error);
      setConnectionStatus('error');

      let errorMessage = "Failed to start system audio recording";
      if (error instanceof Error) {
        if (error.message.includes('Permission denied') || error.message.includes('NotAllowedError')) {
          errorMessage = "Screen sharing permission denied. Please allow screen sharing and check 'Share audio' option.";
        } else if (error.message.includes('No audio tracks available')) {
          errorMessage = "No audio tracks found. Please ensure 'Share audio' is checked when prompted for screen sharing.";
        } else if (error.message.includes('timeout')) {
          errorMessage = "Connection to transcription service timed out";
        } else if (error.message.includes('NotSupportedError')) {
          errorMessage = "System audio capture not supported in this browser. Try Chrome or Edge.";
        } else if (error.message.includes('NotReadableError')) {
          errorMessage = "Audio device is busy or unavailable. Close other applications using audio and try again.";
        }
      }

      showToast("Error", errorMessage, "error");
    }
  }, [startAudioLevelMonitoring, createDeepgramConnection, showToast]);

  // Stop recording function
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }

    // Stop audio level monitoring
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (audioContextRef.current) {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // Close Deepgram connection
    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      socketRef.current.close(1000, 'Recording stopped');
      socketRef.current = null;
    }

    // Clean up transcription
    setTranscription(prev => prev.replace(/\s*\[interim\].*$/, '').trim());
    setConnectionStatus('disconnected');
  }, [isRecording]);



  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === "recording") {
        mediaRecorderRef.current.stop();
        mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      }

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      if (audioContextRef.current) {
        audioContextRef.current.close();
      }

      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.close();
      }
    };
  }, []);

  // Toggle editing mode
  const toggleEditing = useCallback(() => {
    if (!isEditing) {
      setIsEditing(true);
      const cleanTranscription = transcription.replace(/\s*\[interim\].*$/, '').trim();
      setEditedTranscription(cleanTranscription);
      setTimeout(() => {
        if (textAreaRef.current) {
          textAreaRef.current.focus();
        }
      }, 0);
    } else {
      setIsEditing(false);
      if (editedTranscription.trim()) {
        setTranscription(editedTranscription.trim());
      }
    }
  }, [isEditing, transcription, editedTranscription]);

  // Submit transcription
  const handleSubmit = useCallback(() => {
    if (isEditing) {
      setIsEditing(false);
      if (editedTranscription.trim()) {
        setTranscription(editedTranscription.trim());
      }
    }

    const finalTranscription = isEditing
      ? editedTranscription.trim()
      : transcription.replace(/\s*\[interim\].*$/, '').trim();

    if (finalTranscription) {
      onTranscriptionComplete(finalTranscription);
    } else {
      showToast("Error", "No transcription to submit", "error");
    }
  }, [transcription, editedTranscription, isEditing, onTranscriptionComplete, showToast]);

  // Toggle recording function for external control
  const toggleRecording = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else if (!isProcessing && deepgramApiKey) {
      startRecording();
    }
  }, [isRecording, isProcessing, deepgramApiKey, startRecording, stopRecording]);

  // Expose methods to parent components
  useImperativeHandle(ref, () => ({
    toggleRecording,
    toggleEdit: toggleEditing,
    submit: handleSubmit
  }), [toggleRecording, toggleEditing, handleSubmit]);

  // Connection status indicator
  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500';
      case 'connecting': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected to Deepgram';
      case 'connecting': return 'Connecting...';
      case 'error': return 'Connection failed';
      default: return 'Disconnected';
    }
  };

  return (
    <div className="w-full space-y-4">
      {/* Audio level indicator */}
      {isRecording && (
        <div className="w-full h-3 bg-gray-700 rounded-full overflow-hidden">
          <div
            className="h-full bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 transition-all duration-75"
            style={{ width: `${audioLevel}%` }}
          />
        </div>
      )}

      {/* Connection status */}
      <div className="flex justify-between items-center text-xs">
        <div className={`flex items-center gap-2 ${getConnectionStatusColor()}`}>
          <div className={`h-2 w-2 rounded-full ${
            connectionStatus === 'connected' ? 'bg-green-500 animate-pulse' :
            connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
            connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-500'
          }`} />
          <span>{getConnectionStatusText()}</span>
        </div>

        {isTranscribing && (
          <div className="flex items-center gap-2 text-blue-400">
            <div className="animate-spin h-3 w-3 border border-blue-400 border-t-transparent rounded-full"></div>
            <span>Live transcription active</span>
          </div>
        )}
      </div>

      {/* Transcription display or editing area */}
      <div className="relative min-h-[200px] max-h-[400px] overflow-auto p-4 bg-[rgba(22,27,34,0.5)] border border-[rgba(255,255,255,0.1)] rounded-md">
        {isEditing ? (
          <textarea
            ref={textAreaRef}
            value={editedTranscription}
            onChange={(e) => setEditedTranscription(e.target.value)}
            className="w-full h-full min-h-[180px] bg-transparent text-[13px] leading-[1.6] text-gray-100 resize-none focus:outline-none"
            placeholder="Edit your system audio transcription here..."
          />
        ) : (
          <div className="text-[13px] leading-[1.6] text-gray-100 whitespace-pre-line">
            {transcription ? (
              <>
                {transcription.includes('[interim]') ? (
                  <>
                    {transcription.split('[interim]')[0]}
                    <span className="text-gray-400 italic">
                      {transcription.split('[interim]')[1]}
                    </span>
                  </>
                ) : (
                  transcription
                )}
              </>
            ) 
            :
             (
              <span className="text-gray-500"></span>
            )
            }
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4 text-xs text-gray-400">
          {!deepgramApiKey && (
            <span className="text-red-400">⚠ Deepgram API key required</span>
          )}
          
        </div>
        <div className="flex gap-2">
          {isRecording ? (
            <Button
              onClick={stopRecording}
              disabled={isProcessing}
              variant="destructive"
              size="sm"
              className="flex items-center gap-1"
              title="Stop Recording (Alt+4)"
            >
              <StopCircle className="h-4 w-4" />
              Stop Recording
            </Button>
          ) : (
            <>
              <Button
                onClick={startRecording}
                disabled={isProcessing || !deepgramApiKey}
                variant="default"
                size="sm"
                className="flex items-center gap-1"
                title="Capture System Audio (Alt+4)"
              >
                <Volume2 className="h-4 w-4" />
                Capture System Audio
              </Button>

              {transcription && !isEditing && (
                <Button
                  onClick={toggleEditing}
                  disabled={isProcessing}
                  variant="outline"
                  size="sm"
                  title="Edit Text (Alt+5)"
                >
                  Edit Text
                </Button>
              )}

              {isEditing && (
                <Button
                  onClick={toggleEditing}
                  disabled={isProcessing}
                  variant="outline"
                  size="sm"
                  title="Save Edits (Alt+5)"
                >
                  Save Edits
                </Button>
              )}

              {transcription && (
                <Button
                  onClick={handleSubmit}
                  disabled={isProcessing}
                  size="sm"
                  title="Submit Transcription (Alt+6)"
                >
                  Submit
                </Button>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const ForwardedVoiceInputComponent = forwardRef(VoiceInputComponent);
ForwardedVoiceInputComponent.displayName = "VoiceInputComponent";

export default ForwardedVoiceInputComponent;